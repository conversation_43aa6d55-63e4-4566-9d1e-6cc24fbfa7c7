import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:sf_app_v2/core/api/network/network_helper.dart';
import 'package:sf_app_v2/core/api/network/network_logger.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/routes/navigator.dart';
import 'package:sf_app_v2/core/utils/secure_storage_helper.dart';

import '../../config/app_config.dart';
import '../../constants/enums.dart';

const enableLogs = true;

log(message, {name}) {
  if (enableLogs) dev.log(message, name: name ?? '');
}

class NetworkProvider {
  final Dio _dio;
  static final Map<String, Response> _cache = {};
  static bool _hasShownUnauthorizedError = false;

  NetworkProvider({String? baseUrl})
      : _dio = Dio(
          BaseOptions(
            baseUrl: baseUrl ?? AppConfig.instance.baseUrl,
            headers: {
              "Content-Type": "application/json",
              "Accept-Language": CommonFunctions().getLanguageLocaleCode(),
            },
          ),
        ) {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      RetryInterceptor(
        dio: _dio,
        logPrint: print,
        retries: 3,
        retryEvaluator: (error, attempt) => _retryEvaluator()(error, attempt),
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // NetworkLogger.logRequest(options);

          if (options.headers.containsKey('auth')) {
            options.headers.remove('auth');
            final String? token = await SecureStorageHelper()
                .readSecureData(LocalStorageKeys.token);
            if (token != null && token.isNotEmpty) {
              options.headers.addEntries({'Authorization': token}.entries);
            }
          }

          return handler.next(options);
        },
        onResponse: (response, handler) {
          NetworkLogger.logResponse(response);
          return handler.next(response);
        },
        onError: (error, handler) async {
          NetworkLogger.logError(error);

          if (error.requestOptions.extra.containsKey('retry')) {
            try {
              String? accessToken = await _refreshToken();
              if (accessToken != null) {
                error.requestOptions.headers['Authorization'] =
                    'Bearer $accessToken';
                error.requestOptions.extra['retry'] = true;
                final response = await _dio.request(
                  error.requestOptions.path,
                  options: Options(
                    method: error.requestOptions.method,
                    headers: error.requestOptions.headers,
                    responseType: error.requestOptions.responseType,
                  ),
                  queryParameters: error.requestOptions.queryParameters,
                );
                return handler.resolve(response);
              }
            } catch (refreshError) {
              return handler.reject(
                DioException(
                  requestOptions: error.requestOptions,
                  error: 'Failed to refresh token',
                ),
              );
            }
          } else if (error.response?.statusCode == 401 &&
              (error.requestOptions.path != '/login')) {
            if (!_hasShownUnauthorizedError) {
              _hasShownUnauthorizedError = true;
              NetworkHelper.handleMessage(
                CommonFunctions().errorMapping(error.response) ??
                    'Unknown error occurred',
                navigatorKey.currentContext!,
                type: HandleTypes.customDialog,
                snackBarType: SnackBarType.error,
                onTap: () {
                  CommonFunctions.logoutUser(
                    navigatorKey.currentContext!,
                    isNavToMain: true,
                  );
                  _hasShownUnauthorizedError = false;
                },
              );
            }
          }

          return handler.next(
            DioException(
              requestOptions: error.requestOptions,
              response: error.response,
              error: CommonFunctions().errorMapping(error.response) ??
                  'Unknown error occurred',
            ),
          );
        },
      ),
    );
  }

  RetryEvaluator _retryEvaluator() => (error, attempt) {
        if (error.requestOptions.method != 'GET') return false;

        final statusCode = error.response?.statusCode;
        if (statusCode == 400 ||
            statusCode == 401 ||
            statusCode == 403 ||
            statusCode == 404) {
          return false;
        }

        return true;
      };

  Future<Response<T>> _makeRequest<T>(
    RequestType method,
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    final cacheKey =
        _generateCacheKey(method, path, data ?? queryParameters ?? {});

    if (_cache.containsKey(cacheKey) && force && method == RequestType.get) {
      return _cache[cacheKey]! as Response<T>;
    }
    if (isSigninRequired) {
      options ??= Options();
      options.headers ??= {};
      options.headers!.addAll({'auth': true});
    }
    try {
      Response<T> response;
      switch (method) {
        case RequestType.get:
          response = await _dio.get<T>(
            path,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onReceiveProgress: onReceiveProgress,
          );
          break;
        case RequestType.post:
          response = await _dio.post<T>(
            path,
            data: CommonFunctions().removeNullValues(data ?? {}),
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );
          break;
        case RequestType.put:
          response = await _dio.put<T>(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
            onSendProgress: onSendProgress,
            onReceiveProgress: onReceiveProgress,
          );
          break;
        case RequestType.delete:
          response = await _dio.delete<T>(
            path,
            data: data,
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
          );
          break;
        case RequestType.patch:
          response = await _dio.patch<T>(
            path,
            data: CommonFunctions().removeNullValues(data ?? {}),
            queryParameters: queryParameters,
            options: options,
            cancelToken: cancelToken,
          );
          break;
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        _cache[cacheKey] = response;
      }

      return response;
    } catch (error) {
      return Future.error(error);
    }
  }

  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onReceiveProgress,
    bool isSigninRequired = false,
    bool force = false,
  }) async {
    return _makeRequest<T>(
      RequestType.get,
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> post<T>(
    String path, {
    Map<String, dynamic>? data,
    FormData? formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.post,
      path,
      data: data ?? formData,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> put<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.put,
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> delete<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.delete,
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> patch<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    return _makeRequest<T>(
      RequestType.patch,
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      force: force,
      isSigninRequired: isSigninRequired,
    );
  }

  Future<Response<T>> formData<T>(
    String path, {
    FormData? formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = false,
    bool isSigninRequired = false,
  }) async {
    if (isSigninRequired) {
      options ??= Options();
      options.headers ??= {};
      options.headers!.addAll({'auth': true});
    }
    return _dio.post<T>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  Future<Response<T>> retryRequest<T>(RequestOptions requestOptions) async {
    final Completer<Response<T>> responseCompleter = Completer<Response<T>>();

    responseCompleter.complete(
      request<T>(
        requestOptions,
      ),
    );

    return responseCompleter.future;
  }

  Future<Response<T>> request<T>(RequestOptions requestOptions) async {
    return _dio.request<T>(
      requestOptions.path,
      cancelToken: requestOptions.cancelToken,
      data: requestOptions.data,
      onReceiveProgress: requestOptions.onReceiveProgress,
      onSendProgress: requestOptions.onSendProgress,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        sendTimeout: requestOptions.sendTimeout,
        receiveTimeout: requestOptions.receiveTimeout,
        extra: requestOptions.extra,
        headers: requestOptions.headers,
        responseType: requestOptions.responseType,
        contentType: requestOptions.contentType,
        validateStatus: requestOptions.validateStatus,
        receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
        followRedirects: requestOptions.followRedirects,
        maxRedirects: requestOptions.maxRedirects,
        persistentConnection: requestOptions.persistentConnection,
        requestEncoder: requestOptions.requestEncoder,
        responseDecoder: requestOptions.responseDecoder,
        listFormat: requestOptions.listFormat,
      ),
    );
  }

  String _generateCacheKey(
    RequestType method,
    String url,
    Map<String, dynamic> data,
  ) {
    final methodString = method.name;
    final dataString = jsonEncode(data);
    return '$methodString|$url|$dataString';
  }

  Future<String?> _refreshToken() async {
    try {
      String? refreshToken = await SecureStorageHelper()
          .readSecureData(LocalStorageKeys.refreshToken);
      Response response = await Dio().post(
        '${AppConfig.instance.baseUrl}/auth/refresh_token',
        data: {"refreshToken": refreshToken},
      );
      if (response.statusCode == 200) {
        String? newAuthToken = response.data['data']['accessToken'];
        String? newRefreshToken = response.data['data']['refreshToken'];
        await SecureStorageHelper()
            .writeSecureData(LocalStorageKeys.token, newAuthToken ?? '');
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.refreshToken,
          newRefreshToken ?? '',
        );
        return newAuthToken;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static void clearCache() {
    _cache.clear();
  }
}
