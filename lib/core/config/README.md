# Refactored Configuration System

This document describes the new, refactored configuration system that follows clean architecture principles and provides better separation of concerns.

## Overview

The new configuration system addresses several issues with the previous implementation:

- **Separation of Concerns**: Different types of configuration are now separated into focused modules
- **Dependency Injection**: Proper DI integration using Injectable
- **Type Safety**: Immutable configurations using Freezed
- **Feature-First Architecture**: Aligns with the app's feature-first structure
- **Environment Abstraction**: Clear separation between environment and flavor configurations

## Structure

```
lib/core/config/
├── environment_config.dart         # Environment-specific settings (debug/prod)
├── refactored_app_config.dart      # Main configuration interface
├── config_module.dart              # DI registration
├── feature_configs/                # Feature-specific configurations
│   ├── payment_feature_config.dart
│   ├── investment_feature_config.dart
│   └── ui_feature_config.dart
├── flavors/                        # App flavor configurations
│   ├── base_flavor_config.dart
│   └── sf_flavor_config.dart
└── migration_example.dart          # Usage examples
```

## Key Components

### 1. Environment Configuration
Handles environment-specific settings (debug vs production):
- API URLs
- WebSocket URLs
- Debug flags
- Logging settings

### 2. Feature Configurations
Separate configurations for different features:
- **PaymentFeatureConfig**: Payment types, wallet settings
- **InvestmentFeatureConfig**: Purchase percentages, investment features
- **UiFeatureConfig**: UI feature flags, theme settings

### 3. Flavor Configuration
App-specific configurations that combine all feature configs for a specific app variant.

### 4. Services
Feature-specific services that encapsulate business logic:
- **PaymentFeatureService**: Payment-related operations
- **InvestmentFeatureService**: Investment-related operations

## Usage

### Basic Configuration Access
```dart
@injectable
class MyService {
  final RefactoredAppConfig config;
  
  MyService(this.config);
  
  void useConfig() {
    final appName = config.appName;
    final baseUrl = config.baseUrl;
    final isDebug = config.isDebug;
  }
}
```

### Feature-Specific Services
```dart
@injectable
class MyPaymentService {
  final PaymentFeatureService paymentService;
  
  MyPaymentService(this.paymentService);
  
  void handlePayments() {
    final paymentTypes = paymentService.getAvailablePaymentTypes();
    final shouldFetch = paymentService.shouldFetchWalletCoinsFromApi;
  }
}
```

### Widget Usage
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final config = getIt<RefactoredAppConfig>();
    
    return Text(config.appName);
  }
}
```

## Migration Guide

### From Old System
```dart
// OLD
final appName = AppConfig.instance.appName;
final paymentTypes = AppConfig.getAvailablePaymentTypes();

// NEW
final config = getIt<RefactoredAppConfig>();
final paymentService = getIt<PaymentFeatureService>();

final appName = config.appName;
final paymentTypes = paymentService.getAvailablePaymentTypes();
```

## Benefits

1. **Better Testability**: Easy to mock individual configuration components
2. **Cleaner Dependencies**: Clear separation of what each service needs
3. **Type Safety**: Compile-time checking with Freezed models
4. **Maintainability**: Easier to add new features and configurations
5. **Performance**: Lazy loading and proper singleton management
6. **Consistency**: Follows the app's existing architecture patterns

## Adding New Features

To add a new feature configuration:

1. Create a new config class in `feature_configs/`
2. Add the config to `FlavorConfig`
3. Create a service class for the feature
4. Register in `ConfigModule`
5. Update `RefactoredAppConfig` if needed

## Next Steps

1. **Gradual Migration**: Start using the new system for new features
2. **Update Existing Code**: Gradually migrate existing code to use the new system
3. **Remove Old System**: Once migration is complete, remove the old configuration files
4. **Add Tests**: Write comprehensive tests for the new configuration system
