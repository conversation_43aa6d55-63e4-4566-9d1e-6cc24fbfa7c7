import 'package:sf_app_v2/core/config/app_config.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/wallet/deposit/domain/models/wallet_coin/wallet_coin.dart';

import '../models/app_config/payment_type_model.dart';

/// Represents a payment type configuration item

/// App configuration for payment types based on flavor
class PaymentConfig {
  /// Static payment type configurations for non-SIS flavors
  static const List<PaymentTypeModel> _staticPaymentTypes = [
    PaymentTypeModel(
      code: 'TRC20',
      name: 'TRC20 (USDT)',
      networkType: PaymentType.TRC20,
      icon: Assets.trcIcon,
      id: 1,
    ),
    PaymentTypeModel(
      code: 'ERC20',
      name: 'ERC20 (USDT)',
      networkType: PaymentType.ERC20,
      icon: Assets.ercIcon,
      id: 2,
    ),
  ];

  static const List<PaymentTypeModel> _staticPaymentTypesInAddWithdrawAddress = [
    PaymentTypeModel(
      code: 'TRC20_USDT',
      name: 'TRC20 (USDT)',
      networkType: PaymentType.TRC20,
      icon: Assets.trcIcon,
      id: 1,
    ),
    PaymentTypeModel(
      code: 'ERC20_USDT',
      name: 'ERC20 (USDT)', 
      networkType: PaymentType.ERC20,
      icon: Assets.ercIcon,
      id: 2,
    ),
    PaymentTypeModel(
      code: 'TRC20_USDC',
      name: 'TRC20 (USDC)',
      networkType: PaymentType.TRC20, 
      icon: Assets.trcIcon,
      id: 3,
    ),
    PaymentTypeModel(
      code: 'ERC20_USDC',
      name: 'ERC20 (USDC)',
      networkType: PaymentType.ERC20,
      icon: Assets.ercIcon,
      id: 4,
    ),
  ];

  /// Gets the available payment types for the current flavor
  static List<PaymentTypeModel> getAvailablePaymentTypes({
    List<WalletCoinData>? apiWalletCoins,
  }) {
    if (AppConfig.instance.showStaticPaymentTypes) return _staticPaymentTypes;
    final hasApiData = apiWalletCoins?.isNotEmpty ?? false;
    return hasApiData
        ? apiWalletCoins!.map(PaymentTypeModel.fromWalletCoin).toList()
        : _staticPaymentTypes;
  }

  /// Gets the default payment type for the current flavor
  static PaymentTypeModel? getDefaultPaymentType({
    List<WalletCoinData>? apiWalletCoins,
  }) {
    final availableTypes =
        getAvailablePaymentTypes(apiWalletCoins: apiWalletCoins);
    return availableTypes.isNotEmpty ? availableTypes.first : null;
  }

  /// Checks if wallet coins API should be called for the current flavor
  static bool get shouldFetchWalletCoinsFromApi =>
      !AppConfig.instance.showStaticPaymentTypes;

  /// Gets PaymentType enum from payment code
  static PaymentType getPaymentTypeFromCode(String paymentCode) {
    return paymentCode.contains('ERC20')
        ? PaymentType.ERC20
        : PaymentType.TRC20;
  }

  static List<PaymentTypeModel> getStaticPaymentTypesInAddWithdrawAddress() {
    return _staticPaymentTypesInAddWithdrawAddress;
  }
}
