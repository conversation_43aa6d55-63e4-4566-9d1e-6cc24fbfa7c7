import 'package:sf_app_v2/core/config/app_config.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/purchase_percentage/purchase_percentage.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/order_rate/order_rate.dart';

/// Simplified configuration class to manage purchase percentage rates
class PurchasePercentageConfig {
  // Static default purchase percentages
  static final List<PurchasePercentage> _static = [
    PurchasePercentage(percentage: '30', isSelected: false, type: 1),
    PurchasePercentage(percentage: '50', isSelected: false, type: 2),
    PurchasePercentage(percentage: '100', isSelected: true, type: 3),
  ];

  /// Determines if API mode should be used
  static bool isApiMode(OrderRateData? apiData) {
    final config = AppConfig.instance;
    return config.showSmartInvestmentPurchasePercentage &&
        !config.showStaticPurchasePercentage &&
        !isApiConfigEmpty(apiData);
  }

  /// Checks if the API configuration is empty or invalid
  static bool isApiConfigEmpty(OrderRateData? apiData) {
    return apiData == null ||
        apiData.orderRates == null ||
        apiData.orderRates!.isEmpty;
  }

  /// Gets the available purchase percentages for the current configuration
  static List<PurchasePercentage> getList({
    OrderRateData? apiData,
    String? selected,
  }) {
    if (isApiMode(apiData)) {
      return _fromApi(apiData!, selected);
    }
    return _fromStatic(selected);
  }

  /// Gets the default selected purchase percentage
  static String getDefault({OrderRateData? apiData}) {
    if (isApiMode(apiData)) {
      // Use the highest percentage from API as default
      final max = apiData!.orderRates!.reduce((a, b) => a > b ? a : b);
      return max.toString();
    }
    return '100';
  }

  /// Checks if a specific percentage is enabled for selection
  static bool isEnabled(String percentage, {OrderRateData? apiData}) {
    if (isApiMode(apiData)) {
      final val = int.tryParse(percentage);
      return val != null && apiData!.orderRates!.contains(val);
    }
    return percentage == '100';
  }

  // --- Internal helpers ---

  static List<PurchasePercentage> _fromApi(
      OrderRateData apiData, String? selected) {
    final def = getDefault(apiData: apiData);
    final sel = selected ?? def;
    return apiData.orderRates!
        .map((rate) => PurchasePercentage(
              percentage: rate.toString(),
              isSelected: rate.toString() == sel,
              type: rate,
            ))
        .toList();
  }

  static List<PurchasePercentage> _fromStatic(String? selected) {
    final sel = selected ?? '100';
    return _static
        .map((item) => PurchasePercentage(
              percentage: item.percentage,
              isSelected: item.percentage == sel,
              type: item.type,
            ))
        .toList();
  }
}
