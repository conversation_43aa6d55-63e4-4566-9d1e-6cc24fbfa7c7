// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/app_update/domain/repository/app_update_repository.dart'
    as _i122;
import '../../features/app_update/domain/services/app_update_service.dart'
    as _i1035;
import '../../features/app_update/logic/app_update/app_update_cubit.dart'
    as _i499;
import '../../features/auth/account_info/domain/repository/account_info_repository.dart'
    as _i81;
import '../../features/auth/account_info/domain/services/account_info_service.dart'
    as _i460;
import '../../features/auth/account_info/logic/account_info/account_info_cubit.dart'
    as _i618;
import '../../features/auth/account_info/logic/google_authentication/google_authentication_cubit.dart'
    as _i962;
import '../../features/auth/account_info/logic/phone/phone_cubit.dart' as _i303;
import '../../features/auth/account_info/logic/wallet/wallet_cubit.dart'
    as _i24;
import '../../features/auth/sign_in/domain/repository/sign_in_repository.dart'
    as _i17;
import '../../features/auth/sign_in/domain/services/sign_in_service.dart'
    as _i424;
import '../../features/auth/sign_in/logic/sign_in/sign_in_cubit.dart' as _i464;
import '../../features/auth/sign_up/domain/repository/sign_up_repository.dart'
    as _i988;
import '../../features/auth/sign_up/domain/services/sign_up_service.dart'
    as _i449;
import '../../features/auth/sign_up/logic/sign_up/sign_up_cubit.dart' as _i885;
import '../../features/auth/verify/logic/verify/verify_cubit.dart' as _i502;
import '../../features/community/domain/repository/community_repository.dart'
    as _i62;
import '../../features/community/domain/services/community_service.dart'
    as _i108;
import '../../features/community/logic/community/community_cubit.dart' as _i463;
import '../../features/finance/domain/repository/finance_repository.dart'
    as _i825;
import '../../features/finance/domain/services/finance_service.dart' as _i680;
import '../../features/finance/logic/finance/finance_cubit.dart' as _i382;
import '../../features/home/<USER>/repository/home_repository.dart' as _i541;
import '../../features/home/<USER>/services/home_service.dart' as _i677;
import '../../features/home/<USER>/home/<USER>' as _i906;
import '../../features/market_v2/domain/repositories/market_repository.dart'
    as _i738;
import '../../features/market_v2/domain/service/market_service.dart' as _i378;
import '../../features/market_v2/logic/market/market_cubit.dart' as _i972;
import '../../features/market_v2/logic/search/search_cubit.dart' as _i764;
import '../../features/news/domain/repository/news_repository.dart' as _i1049;
import '../../features/news/domain/services/news_service.dart' as _i857;
import '../../features/news/logic/news/news_cubit.dart' as _i3;
import '../../features/notification/domain/repository/notification_repository.dart'
    as _i1057;
import '../../features/notification/domain/services/notification_service.dart'
    as _i641;
import '../../features/notification/logic/notification/notification_cubit.dart'
    as _i588;
import '../../features/profile/domain/repository/profile_repository.dart'
    as _i364;
import '../../features/profile/domain/services/profile_service.dart' as _i770;
import '../../features/profile/logic/profile/profile_cubit.dart' as _i99;
import '../../features/smart_investment/domain/repository/smart_investment_repository.dart'
    as _i890;
import '../../features/smart_investment/domain/services/smart_investment_service.dart'
    as _i286;
import '../../features/smart_investment/logic/smart_investment/smart_investment_cubit.dart'
    as _i263;
import '../../features/support/domain/repository/support_repository.dart'
    as _i762;
import '../../features/support/domain/services/support_service.dart' as _i708;
import '../../features/support/logic/support/support_cubit.dart' as _i520;
import '../../features/transfer/domain/repository/transfer_repository.dart'
    as _i252;
import '../../features/transfer/domain/services/transfer_service.dart' as _i822;
import '../../features/transfer/logic/transfer/transfer_cubit.dart' as _i448;
import '../../features/wallet/deposit/domain/repository/deposit_repository.dart'
    as _i867;
import '../../features/wallet/deposit/domain/services/deposit_service.dart'
    as _i593;
import '../../features/wallet/deposit/logic/deposit/deposit_cubit.dart'
    as _i274;
import '../../features/wallet/records/domain/repository/records_repository.dart'
    as _i676;
import '../../features/wallet/records/domain/services/records_service.dart'
    as _i936;
import '../../features/wallet/records/logic/records/records_cubit.dart'
    as _i313;
import '../../features/wallet/withdraw/domain/repository/withdraw_repository.dart'
    as _i172;
import '../../features/wallet/withdraw/domain/services/withdraw_service.dart'
    as _i174;
import '../../features/wallet/withdraw/logic/withdraw/withdraw_cubit.dart'
    as _i178;
import '../config/config_module.dart' as _i557;
import '../config/environment_config.dart' as _i424;
import '../config/feature_configs/investment_feature_config.dart' as _i775;
import '../config/feature_configs/payment_feature_config.dart' as _i578;
import '../config/feature_configs/ui_feature_config.dart' as _i147;
import '../config/flavors/base_flavor_config.dart' as _i355;
import '../config/refactored_app_config.dart' as _i945;
import '../services/app_update/app_download_service.dart' as _i626;
import '../services/app_update/app_update_dialog_service.dart' as _i506;
import '../services/country_code/country_code_repository.dart' as _i631;
import '../services/country_code/country_code_service.dart' as _i1058;
import '../services/image_picker/image_picker_repository.dart' as _i531;
import '../services/image_picker/image_picker_service.dart' as _i818;
import '../services/language/language_repository.dart' as _i258;
import '../services/language/language_service.dart' as _i63;
import '../services/phone_country/phone_country_service.dart' as _i844;
import '../services/wallet_balance/wallet_balance_service.dart' as _i89;
import '../shared/logic/chat_button/chat_button_cubit.dart' as _i54;
import '../shared/logic/country_code/country_code_cubit.dart' as _i880;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final configModule = _$ConfigModule();
    gh.factory<_i626.AppDownloadService>(() => _i626.AppDownloadService());
    gh.singleton<_i424.EnvironmentConfig>(() => configModule.environmentConfig);
    gh.singleton<_i355.FlavorConfig>(() => configModule.flavorConfig);
    gh.singleton<_i578.PaymentFeatureConfig>(
        () => configModule.paymentFeatureConfig);
    gh.singleton<_i775.InvestmentFeatureConfig>(
        () => configModule.investmentFeatureConfig);
    gh.singleton<_i147.UiFeatureConfig>(() => configModule.uiFeatureConfig);
    gh.singleton<_i54.ChatButtonCubit>(() => _i54.ChatButtonCubit());
    gh.singleton<_i844.PhoneCountryService>(() => _i844.PhoneCountryService());
    gh.singleton<_i89.WalletBalanceService>(() => _i89.WalletBalanceService());
    gh.lazySingleton<_i506.AppUpdateDialogService>(
        () => _i506.AppUpdateDialogService());
    gh.factory<_i252.TransferRepository>(() => _i822.TransferService());
    gh.factory<_i172.WithdrawRepository>(() => _i174.WithdrawService());
    gh.factory<_i364.ProfileRepository>(() => _i770.ProfileService());
    gh.factory<_i17.SignInRepository>(() => _i424.SignInService());
    gh.factory<_i448.TransferCubit>(
        () => _i448.TransferCubit(gh<_i252.TransferRepository>()));
    gh.factory<_i762.SupportRepository>(() => _i708.SupportService());
    gh.factory<_i531.ImagePickerRepository>(() => _i818.ImagePickerService());
    gh.factory<_i122.AppUpdateRepository>(() => _i1035.AppUpdateService());
    gh.factory<_i890.SmartInvestmentRepository>(
        () => _i286.SmartInvestmentService());
    gh.lazySingleton<_i258.LanguageRepository>(() => _i63.LanguageService());
    gh.factory<_i867.DepositRepository>(() => _i593.DepositService());
    gh.factory<_i62.CommunityRepository>(() => _i108.CommunityService());
    gh.factory<_i631.CountryCodeRepository>(() => _i1058.CountryCodeService());
    gh.factory<_i738.MarketRepository>(() => _i378.MarketService());
    gh.factory<_i541.HomeRepository>(() => _i677.HomeService());
    gh.factory<_i81.AccountInfoRepository>(() => _i460.AccountInfoService());
    gh.factory<_i178.WithdrawCubit>(
        () => _i178.WithdrawCubit(gh<_i172.WithdrawRepository>()));
    gh.factory<_i825.FinanceRepository>(() => _i680.FinanceService());
    gh.factory<_i1049.NewsRepository>(() => _i857.NewsService());
    gh.factory<_i988.SignUpRepository>(() => _i449.SignUpService());
    gh.factory<_i463.CommunityCubit>(
        () => _i463.CommunityCubit(gh<_i62.CommunityRepository>()));
    gh.factory<_i1057.NotificationRepository>(
        () => _i641.NotificationService());
    gh.factory<_i676.RecordsRepository>(() => _i936.RecordsService());
    gh.factory<_i464.SignInCubit>(
        () => _i464.SignInCubit(gh<_i17.SignInRepository>()));
    gh.factory<_i502.VerifyCubit>(
        () => _i502.VerifyCubit(gh<_i988.SignUpRepository>()));
    gh.factory<_i885.SignUpCubit>(
        () => _i885.SignUpCubit(gh<_i988.SignUpRepository>()));
    gh.factory<_i274.DepositCubit>(
        () => _i274.DepositCubit(gh<_i867.DepositRepository>()));
    gh.factory<_i588.NotificationCubit>(
        () => _i588.NotificationCubit(gh<_i1057.NotificationRepository>()));
    gh.factory<_i3.NewsCubit>(() => _i3.NewsCubit(gh<_i1049.NewsRepository>()));
    gh.factory<_i764.SearchCubit>(
        () => _i764.SearchCubit(gh<_i738.MarketRepository>()));
    gh.factory<_i972.MarketCubit>(
        () => _i972.MarketCubit(gh<_i738.MarketRepository>()));
    gh.factory<_i24.WalletCubit>(
        () => _i24.WalletCubit(gh<_i81.AccountInfoRepository>()));
    gh.factory<_i303.PhoneCubit>(
        () => _i303.PhoneCubit(gh<_i81.AccountInfoRepository>()));
    gh.factory<_i499.AppUpdateCubit>(() => _i499.AppUpdateCubit(
          gh<_i122.AppUpdateRepository>(),
          gh<_i626.AppDownloadService>(),
        ));
    gh.factory<_i775.InvestmentFeatureService>(() =>
        _i775.InvestmentFeatureService(gh<_i775.InvestmentFeatureConfig>()));
    gh.factory<_i99.ProfileCubit>(
        () => _i99.ProfileCubit(gh<_i364.ProfileRepository>()));
    gh.factory<_i578.PaymentFeatureService>(
        () => _i578.PaymentFeatureService(gh<_i578.PaymentFeatureConfig>()));
    gh.factory<_i520.SupportCubit>(
        () => _i520.SupportCubit(gh<_i762.SupportRepository>()));
    gh.factory<_i263.SmartInvestmentCubit>(() =>
        _i263.SmartInvestmentCubit(gh<_i890.SmartInvestmentRepository>()));
    gh.factory<_i618.AccountInfoCubit>(() => _i618.AccountInfoCubit(
          gh<_i81.AccountInfoRepository>(),
          gh<_i531.ImagePickerRepository>(),
        ));
    gh.factory<_i382.FinanceCubit>(
        () => _i382.FinanceCubit(gh<_i825.FinanceRepository>()));
    gh.factory<_i880.CountryCodeCubit>(() => _i880.CountryCodeCubit(
          gh<_i631.CountryCodeRepository>(),
          gh<_i844.PhoneCountryService>(),
        ));
    gh.factory<_i313.RecordsCubit>(
        () => _i313.RecordsCubit(gh<_i676.RecordsRepository>()));
    gh.factory<_i962.GoogleAuthenticationCubit>(() =>
        _i962.GoogleAuthenticationCubit(gh<_i81.AccountInfoRepository>()));
    gh.factory<_i906.HomeCubit>(
        () => _i906.HomeCubit(gh<_i541.HomeRepository>()));
    gh.singleton<_i945.RefactoredAppConfig>(() => _i945.RefactoredAppConfig(
          gh<_i424.EnvironmentConfig>(),
          gh<_i355.FlavorConfig>(),
          gh<_i578.PaymentFeatureService>(),
          gh<_i775.InvestmentFeatureService>(),
        ));
    return this;
  }
}

class _$ConfigModule extends _i557.ConfigModule {}
