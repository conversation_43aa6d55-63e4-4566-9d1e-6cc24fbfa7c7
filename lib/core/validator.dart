import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import 'config/refactored_app_config.dart';
import 'dependency_injection/injectable.dart';
import 'constants/string_constants.dart';
import 'utils/functions.dart';

enum InputFormatType { name, phoneNumber, email, password }

mixin Validator {
  String? validatePassword(String? value, {String? msg}) {
    final appConfig = getIt<RefactoredAppConfig>();
    final disabledUpperCase = appConfig.disableUpperCasePasswordProtection;
    final passwordErrorMsg = disabledUpperCase
        ? StringConstants.atLeast8characterWithoutUpperCase.tr()
        : msg;
    String errorMsg = StringConstants.enterValidPassword.tr();
    if ((value ?? '').isEmpty) {
      return passwordErrorMsg ?? StringConstants.emptyPasswordMsg.tr();
    } else if (value!.length < 8) {
      return passwordErrorMsg ?? errorMsg;
    } else if (!value.contains(RegExp(r'[0-9]'))) {
      return passwordErrorMsg ?? errorMsg;
    } else if (!RegExp(disabledUpperCase
        ? r'^(?=.*?[a-z])(?=.*?\d)[^\s]{8,}$'
        : r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\d)[^\s]{8,}$').hasMatch(value)) {
      return passwordErrorMsg ?? errorMsg;
    } else if (!value.contains(RegExp(r'[a-z]'))) {
      return passwordErrorMsg ?? errorMsg;
    }
    return null;
  }

  String? validateLoginPassword(String? value, {String? msg}) {
    if ((msg ?? '').isNotEmpty) {
      return msg;
    } else if ((value ?? '').isEmpty) {
      return StringConstants.emptyPasswordMsg.tr();
    }
    return null;
  }

  String? validateConfirmPassword(
    String? oldPassword,
    String? newPassword,
  ) {
    if ((newPassword ?? '').isEmpty) {
      return StringConstants.emptyPasswordMsg.tr();
    } else if (oldPassword != newPassword) {
      return StringConstants.passwordNotEqual.tr();
    }
    return null;
  }

  String? validateWalletConfirmPassword(
    String? oldPassword,
    String? newPassword,
  ) {
    if ((newPassword ?? '').isEmpty) {
      return StringConstants.emptyWalletMsg.tr();
    } else if (oldPassword != newPassword) {
      return StringConstants.passwordNotEqual.tr();
    }
    return null;
  }

  String? validateEmail(String? value, {String? msg}) {
    // String pattern =
    // //     r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    // String pattern =
    //     r'^(?=.{1,320}$)[a-zA-Z0-9._%+-]{1,64}@[a-zA-Z0-9.-]{1,255}\.[a-zA-Z]{2,63}$';
    String pattern =
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$';

    RegExp regex = RegExp(pattern);
    if (msg != null) return msg;
    if ((value ?? '').isEmpty) return StringConstants.emptyEmailMsg.tr();
    if (!regex.hasMatch(value!)) {
      return StringConstants.emailValidatorMsg.tr();
    } else {
      return null;
    }
  }

  String? validateAadhaarNumber(String value) {
    if (value.isEmpty) {
      return StringConstants.idNumberErrorMessage.tr();
    }
    return null;
  }

  String? validateCharacters(String? value, {String? msg}) {
    if (msg != null) {
      return msg;
    }
    if (value == null || value.isEmpty) {
      return StringConstants.emptyStringMsg.tr();
    } else if (value.length < 3) {
      return StringConstants.nameValidatorMsg(3).tr();
    }
    return null;
  }

  List<TextInputFormatter>? inputFormatter(InputFormatType type) {
    List<TextInputFormatter>? val;
    switch (type) {
      case InputFormatType.phoneNumber:
        val = [
          FilteringTextInputFormatter.allow(RegExp("[0-9]")),
        ];
        break;

      case InputFormatType.password:
        val = [
          FilteringTextInputFormatter.allow(RegExp("[0-9a-zA-Z@]")),
        ];
        break;
      case InputFormatType.name:
        val = [FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z]+|\s"))];
        break;
      case InputFormatType.email:
        val = [FilteringTextInputFormatter.deny(RegExp(r'[- /+?:;*#$%^&*()]'))];
        break;
    }
    return val;
  }

  String? validateWalletPasscode(String? value) {
    if (value == null || value.isEmpty) {
      return StringConstants.emptyStringMsg.tr();
    } else if (value.length != 6) {
      return StringConstants.invalidPassword.tr();
    }
    return null;
  }

  String? validateWithdrawAmount(String? value, double amount) {
    if (value == null || value.isEmpty) {
      return StringConstants.emptyStringMsg.tr();
    }

    double parsedAmount = parseUSDAmount(value);
    if (parsedAmount == 0.0) {
      return StringConstants.emptyStringMsg.tr();
    } else if (parsedAmount > amount) {
      return StringConstants.insufficientBalance.tr();
    }
    //  else if (parsedAmount < 100) {
    //   return StringConstants.withdrawLimit.tr();
    // }
    return null;
  }

  String? validateWalletPassword(String? value) {
    String pattern = r'^\d{6}$';
    RegExp regExp = RegExp(pattern);
    if (value!.isEmpty) {
      return StringConstants.emptyWalletPasswordMsg.tr();
    } else if (!regExp.hasMatch(value)) {
      return StringConstants.enterExactly6Digits.tr();
    } else if (value.length < 6) {
      return StringConstants.customGenValidatorMsg(
        StringConstants.walletPasswordShouldBe.tr(),
      );
    }
    return null;
  }

  String? validateGoogleCode(String? value) {
    String pattern = r'^\d{6}$';
    RegExp regExp = RegExp(pattern);

    if (value!.isEmpty) {
      return StringConstants.enterGoogleCode.tr();
    } else if (!regExp.hasMatch(value)) {
      return StringConstants.enterValidGoogleCode.tr();
    } else if (value.length < 6) {
      return StringConstants.customGenValidatorMsg(
        StringConstants.googleCodeShouldBe.tr(),
      );
    }
    return null;
  }

  String? validateCaptcha(String? value) {
    if (value == null || value.isEmpty) {
      return StringConstants.enterCaptcha.tr();
    }
    // else if (value.length < 3) {
    //   return StringConstants.incorrectCaptcha;
    // }
    return null;
  }

  String? validateOtp(String? value) {
    if (value == null || value.isEmpty) {
      return StringConstants.enterCaptcha.tr();
    }
    return null;
  }

  String? validateAddWalletCheck(
    String? value,
    List list, {
    String? msg,
    bool? isErc,
    String? type,
  }) {
    if (list.contains(value)) return msg ?? '';
    if (value == null || value.isEmpty) {
      if (type == 'name') {
        return StringConstants.emptyAddressNameMsg.tr();
      } else {
        return StringConstants.emptyAddressEmailMsg.tr();
      }
    }

    if (type == 'name') {
      // For names, just check that it's not empty and doesn't start/end with spaces
      String trimmed = value.trim();
      if (trimmed.isEmpty || trimmed != value) {
        return StringConstants.validAddressNameMsg.tr();
      }
    } else {
      // For addresses, use the original regex validation
      String pattern =
          (isErc ?? false) ? r'^0x[a-fA-F0-9]{40}$' : r'^T[a-zA-Z0-9]{33}$';
      RegExp regExp = RegExp(pattern);
      if (!regExp.hasMatch(value)) {
        return (isErc ?? false)
            ? StringConstants.customGenValidatorMsg(
                StringConstants.enterValidErc.tr(),
              )
            : StringConstants.customGenValidatorMsg(
                StringConstants.enterValidTrc.tr(),
              );
      }
    }
    return null;
  }

  String? validateUserName(String? value, {message}) {
    String pattern = r'^[a-zA-Z0-9_-]{4,12}$';
    RegExp regExp = RegExp(pattern);

    if (value == null || value.isEmpty) {
      return message ?? StringConstants.emptyUserNameMsg.tr();
    } else if (!regExp.hasMatch(value)) {
      return message ?? StringConstants.enterValidUsername.tr();
    } else if (value.length < 3) {
      return message ?? StringConstants.nameValidatorMsg(3).tr();
    }
    return null;
  }

  String? validateMobile(String? value) {
    // String pattern = r'^[6-9]\d{9}$';
    //korean mobile pattern
    // String pattern = r'\b(0[1|6|7|8|9])(\d{1,3}-?\d{3,4}-?\d{4})\b';
    //indian mobile pattern
    String pattern = r'^-?(([0-9]*)|(([0-9]*)\.([0-9]*)))$';

    RegExp regExp = RegExp(pattern);
    if ((value ?? '').isEmpty) return StringConstants.enterPhone.tr();
    if (!regExp.hasMatch(value!)) {
      return StringConstants.enterValidPhone.tr();
    }
    return null;
  }

  String? validateOTPCode(String? value) {
    if (value == null || value.isEmpty) {
      return StringConstants.enterEmailCode.tr();
    } else if (value.length != 6) {
      return StringConstants.otpCodeError.tr();
    }
    return null;
  }

  // Validate ID number based on selected ID type
  String? validateIdNumber(
      String value, String idType, List<Map<String, dynamic>> idTypes) {
    if (value.isEmpty) {
      return StringConstants.emptyStringMsg.tr();
    }

    // Find the regex pattern for the selected ID type
    final idTypeInfo = idTypes.firstWhere(
      (item) => item['value'] == idType,
      orElse: () => {'regex': r'^\d{12}$'}, // Default to Aadhaar pattern
    );

    final RegExp regExp = RegExp(idTypeInfo['regex']);

    if (!regExp.hasMatch(value)) {
      return StringConstants.idNumberErrorMessage.tr();
    }

    return null;
  }

  String? validateAddress(String? value, PaymentType paymentType) {
    if ((value ?? '').isEmpty) {
      return StringConstants.enterTransactionHash.tr();
    }

    RegExp regExp = paymentType == PaymentType.ERC20
        ? RegExp(r'^0x[a-fA-F0-9]{64}$') // ERC20: 0x + 64 hex chars
        : RegExp(r'^[a-fA-F0-9]{64}$'); // TRC20: 64 hex chars (no 0x)
    if (!regExp.hasMatch(value!)) {
      return StringConstants.enterValidTransactionHash.tr();
    }

    return null;
  }
}
