import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/features/home/<USER>/models/market/market_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../../../core/common_function.dart';
import '../../../core/config/app_config.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/keys.dart';
import '../../../core/routes/routes.dart';
import '../../../core/widgets/common_shimmer.dart';
import 'market_update_cards.dart';

class MarketUpdateSlider extends StatefulWidget {
  const MarketUpdateSlider({super.key});

  @override
  State<MarketUpdateSlider> createState() => _MarketUpdateSliderState();
}

class _MarketUpdateSliderState extends State<MarketUpdateSlider> {
  late WebSocketChannel channel;
  late Stream stream;
  @override
  void initState() {
    super.initState();
    channel = WebSocketChannel.connect(
      Uri.parse(AppConfig.instance.marketWsUrl),
    );
    stream = channel.stream;
  }

  @override
  void dispose() {
    channel.sink.close();
    super.dispose();
  }

  List<MarketListData>? latestData;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<HomeCubit, HomeState, (DataStatus, MarketListModel?)>(
      selector: (state) => (state.marketListFetchStatus, state.marketListData),
      builder: (context, data) {
        switch (data.$1) {
          case DataStatus.success:
            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: Text(
                        StringConstants.marketUpdates,
                        style: FontPalette.bold16
                            .copyWith(color: ColorPalette.titleColor),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: TextButton(
                        child: Text(
                          StringConstants.seeAll,
                          style: FontPalette.medium10
                              .copyWith(color: ColorPalette.primaryColor),
                        ),
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            routeMarketListScreen,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 168.h,
                  width: 1.sh,
                  child: StreamBuilder(
                    stream: stream,
                    builder: (context, snapshot) {
                      List<MarketListData>? marketData = data.$2?.data;
                      if (snapshot.hasData) {
                        Map socketData = json.decode(snapshot.data);
                        List coins = coinsText.keys.toList();

                        if (coins.contains(socketData['symbol']) &&
                            marketData != null) {
                          double latestPrice = 0;
                          if (latestData != null) {
                            int changingItemIndex = latestData!.indexWhere(
                              (item) => item.symbol == socketData['symbol'],
                            );
                            if (changingItemIndex != -1) {
                              latestPrice = double.parse(
                                latestData![changingItemIndex]
                                    .priceChangePercent,
                              );
                            }
                          }

                          int changingItemIndex = marketData.indexWhere(
                            (item) => item.symbol == socketData['symbol'],
                          );
                          if (changingItemIndex != -1) {
                            MarketListData updatedItem = MarketListData(
                              openPrice: socketData['openPrice'],
                              priceChangePercent:
                                  socketData['priceChangePercent'],
                              symbol: socketData['symbol'],
                              direction: latestPrice >
                                      double.parse(
                                        socketData['priceChangePercent'],
                                      )
                                  ? true
                                  : false,
                            );
                            marketData[changingItemIndex] = updatedItem;
                          }
                          latestData = marketData;
                        }
                      }

                      if (marketData == null) return const SizedBox.shrink();

                      return ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount:
                            (marketData.length) > 6 ? 5 : marketData.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          MarketListData marketListDat = marketData[index];
                          return Padding(
                            padding: EdgeInsets.fromLTRB(
                              index == 0 ? 12 : 0,
                              0,
                              0,
                              0,
                            ).r,
                            child: MarketUpdateCard(
                              width: 136.w,
                              height: 168.h,
                              titleText: CommonFunctions()
                                  .formatMarketSymbol(marketListDat.symbol),
                              openPrice: double.parse(marketListDat.openPrice),
                              priceChangePercent:
                                  marketListDat.priceChangePercent,
                              symbol: marketListDat.symbol,
                              direction: marketListDat.direction ?? false,
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            );
          case DataStatus.loading:
            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: Text(
                        StringConstants.marketUpdates,
                        style: FontPalette.bold16
                            .copyWith(color: ColorPalette.titleColor),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: TextButton(
                        child: Text(
                          StringConstants.seeAll,
                          style: FontPalette.semiBold10
                              .copyWith(color: ColorPalette.primaryColor),
                        ),
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            routeMarketListScreen,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                15.verticalSpace,
                Container(
                  padding: EdgeInsets.only(left: 10.w),
                  height: 200.h,
                  width: 1.sh,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: 3,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return SizedBox(
                        width: 136.w,
                        height: 250.w,
                        child: Stack(
                          children: [
                            Container(
                              margin: const EdgeInsets.all(0),
                              width: 136.w,
                              child: Container(
                                margin:
                                    const EdgeInsets.fromLTRB(10, 20, 10, 0).w,
                                child: Card(
                                  elevation: 0,
                                  margin: const EdgeInsets.all(0),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0).r,
                                  ),
                                  color: Colors.white,
                                  child: CommonShimmer(
                                    br: 20.r,
                                    width: 100.w,
                                    height: 100.w,
                                    color: ColorPalette.shimmerColor,
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 0.w,
                              left: 1,
                              right: 1,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CommonShimmer(
                                    br: 20.r,
                                    width: 32.w,
                                    height: 32.w,
                                    color: ColorPalette.shimmerColor,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          default:
            return SizedBox(
              width: 0.2.sw,
              height: 45.h,
            );
        }
      },
    );
  }
}
