import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';

import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/app_update/logic/app_update/app_update_cubit.dart';
import 'package:sf_app_v2/core/services/app_update/app_update_dialog_service.dart';
// import 'package:sf_app_v2/features/chat_v2/cubit/chat/chat_cubit.dart';
// import 'package:sf_app_v2/features/chat_v2/widgets/ui/chat_badge.dart';
import 'package:sf_app_v2/features/community/screens/community_screen.dart';
import 'package:sf_app_v2/features/home/<USER>/home_screen.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/market_section_screen.dart';
import 'package:sf_app_v2/features/profile/screens/profile_screen.dart';
import 'package:sf_app_v2/features/smart_investment/screens/mentor_list_screen.dart';

import '../../../core/config/refactored_app_config.dart';
import '../../../core/dependency_injection/injectable.dart';
import '../../../core/routes/navigator.dart';
import '../../../core/services/notice_dialog_service.dart';
import '../../../core/shared/logic/chat_button/chat_button_cubit.dart';
import '../../../core/utils/shared_preference_helper.dart';
import '../../../core/widgets/chat_floating_button.dart';
import '../../notification/logic/notification/notification_cubit.dart';

class MainScreen extends StatefulWidget {
  final int? initialTabIndex;

  const MainScreen({super.key, this.initialTabIndex});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with RouteAware {
  // Constants
  static const _scrollAnimationDuration = Duration(milliseconds: 300);
  static const _scrollThreshold = 30.0;
  static const _noticeDialogDelay = Duration(milliseconds: 900);

  // Controllers
  late final ValueNotifier<int> _selectedIndex;
  late final PageController _pageController;
  late final ScrollController _homeScrollController;
  late final ScrollController _marketScrollController;
  late final ValueNotifier<bool> _isHomeAppBarScrolled;
  late final ValueNotifier<bool> _isMarketAppBarScrolled;

  // Overlay management
  OverlayEntry? _overlayEntry;
  StreamSubscription<double>? _positionSubscription;

  // Bottom Navigation Items
  static const List<BottomNavItem> _bottomNavItems = [
    BottomNavItem(
      icon: Assets.iconsHome,
      selectedIcon: Assets.iconsHome,
      label: StringConstants.home,
      requiresAuth: false,
    ),
    BottomNavItem(
      icon: Assets.iconsQuote,
      selectedIcon: Assets.iconsQuote,
      label: StringConstants.stocks,
      requiresAuth: false,
    ),
    BottomNavItem(
      icon: Assets.iconsSmartBuy,
      selectedIcon: Assets.iconsSmartBuy,
      label: StringConstants.buy,
      requiresAuth: false,
    ),
    BottomNavItem(
      icon: Assets.iconsCommunity,
      selectedIcon: Assets.iconsCommunity,
      label: StringConstants.community,
      requiresAuth: true,
    ),
    BottomNavItem(
      icon: Assets.iconsProfile,
      selectedIcon: Assets.iconsProfile,
      label: StringConstants.profile,
      requiresAuth: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializePostFrameCallbacks();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _subscribeToRouteObserver();
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _handleWillPop,
      child: ValueListenableBuilder<int>(
        valueListenable: _selectedIndex,
        builder: _buildScaffold,
      ),
    );
  }

  // Initialization Methods
  void _initializeControllers() {
    final initialIndex = widget.initialTabIndex ?? 0;
    _selectedIndex = ValueNotifier(initialIndex);
    _pageController = PageController(initialPage: initialIndex, keepPage: true);
    _isHomeAppBarScrolled = ValueNotifier(false);
    _isMarketAppBarScrolled = ValueNotifier(false);

    _homeScrollController = ScrollController()..addListener(_handleHomeScroll);
    _marketScrollController = ScrollController()
      ..addListener(_handleMarketScroll);
  }

  void _initializePostFrameCallbacks() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initializeAppServices();
    });
  }

  Future<void> _initializeAppServices() async {
    _showFloatWidget();

    // Initialize services
    context.read<NotificationCubit>().getNotifications();
    final config = getIt<RefactoredAppConfig>();
    if (config.showAppUpdate) {
      context.read<AppUpdateCubit>().checkAppUpdate();
    }

    if (mounted) {
      await _handleFirebase();
    }
  }

  void _subscribeToRouteObserver() {
    final modalRoute = ModalRoute.of(context);
    if (modalRoute != null) {
      routeObserver.subscribe(this, modalRoute);
    }
  }

  // Scroll Handling
  void _handleHomeScroll() {
    if (_homeScrollController.hasClients) {
      _isHomeAppBarScrolled.value =
          _homeScrollController.position.pixels > _scrollThreshold.h;
    }
  }

  void _handleMarketScroll() {
    if (_marketScrollController.hasClients) {
      _isMarketAppBarScrolled.value =
          _marketScrollController.position.pixels > _scrollThreshold.h;
    }
  }

  void _scrollToTop(ScrollController controller) {
    if (!mounted || !controller.hasClients) return;

    controller.animateTo(
      0,
      duration: _scrollAnimationDuration,
      curve: Curves.fastOutSlowIn,
    );
  }

  // Tab Selection Logic
  void _handleTabSelection(int index) {
    if (!mounted) return;

    final bool isCurrentTab = _selectedIndex.value == index;
    final item = _bottomNavItems[index];

    if (item.requiresAuth) {
      _handleAuthenticatedTab(index, isCurrentTab);
    } else {
      _handlePublicTab(index, isCurrentTab);
    }
  }

  void _handleAuthenticatedTab(int index, bool isCurrentTab) {
    context.handleSignedInAction(
      skipAccountCheck: true,
      onTap: () {
        if (!isCurrentTab) {
          _updateSelectedIndex(index);
        }
      },
    );
  }

  void _handlePublicTab(int index, bool isCurrentTab) {
    switch (index) {
      case 0: // Home
        if (isCurrentTab) {
          _scrollToTop(_homeScrollController);
        } else {
          _updateSelectedIndex(index);
        }
        break;
      case 1: // Market
        if (isCurrentTab) {
          _scrollToTop(_marketScrollController);
        } else {
          _updateSelectedIndex(index);
        }
        break;
      default:
        if (!isCurrentTab) {
          _updateSelectedIndex(index);
        }
    }
  }

  void _updateSelectedIndex(int index) {
    if (_selectedIndex.value != index) {
      _selectedIndex.value = index;
      _pageController.jumpToPage(index);
    }
  }

  // Overlay Management
  void _showFloatWidget() {
    _overlayEntry = OverlayEntry(
      builder: (context) => const DraggableChatFloatingButton(),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideChatButton() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // Service Methods
  Future<void> _showNoticeDialog() async {
    await Future.delayed(_noticeDialogDelay);
    if (mounted) {
      await NoticeDialogService.showNoticeDialog(context);
    }
  }

  Future<void> _handleFirebase() async {
    // await FirebaseUtils().handleInitialNotification();
  }

  // Navigation Methods
  Future<bool> _handleWillPop() async {
    if (_selectedIndex.value != 0) {
      _selectedIndex.value = 0;
      return false;
    }
    return true;
  }

  // Cleanup
  void _cleanup() {
    routeObserver.unsubscribe(this);
    _positionSubscription?.cancel();
    _homeScrollController.dispose();
    _marketScrollController.dispose();
    _isHomeAppBarScrolled.dispose();
    _isMarketAppBarScrolled.dispose();
    _pageController.dispose();
    _selectedIndex.dispose();
    _hideChatButton();
  }

  // UI Building Methods
  Widget _buildScaffold(BuildContext context, int selectedValue, _) {
    return MultiBlocListener(
      listeners: _buildBlocListeners(),
      child: Scaffold(
        body: _buildPageView(),
        bottomNavigationBar: _buildBottomNavBar(selectedValue),
      ),
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<NotificationCubit, NotificationState>(
        listenWhen: (previous, current) =>
            previous.notificationsFetchStatus != current.notificationsFetchStatus,
        listener: (context, state) {
          if (state.notifications?.data?.isNotEmpty ?? false) {
            final isLoggedIn =
                SharedPreferenceHelper().getIsLoggedIn() ?? false;
            if (isLoggedIn) {
              _showNoticeDialog();
            }
          }
        },
      ),
      BlocListener<AppUpdateCubit, AppUpdateState>(
        listenWhen: (previous, current) =>
            previous.shouldShowUpdateDialog != current.shouldShowUpdateDialog,
        listener: (context, state) {
          if (state.shouldShowUpdateDialog && state.updateInfo != null) {
            AppUpdateDialogService.showUpdateDialog(context, state.updateInfo!);
          }
        },
      ),
      BlocListener<ChatButtonCubit, ChatButtonState>(
        listenWhen: (previous, current) =>
            previous.showChatButton != current.showChatButton,
        listener: (context, state) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (state.showChatButton) {
              _showFloatWidget();
            } else {
              _hideChatButton();
            }
          });
        },
      ),
    ];
  }

  Widget _buildPageView() {
    return PageView(
      onPageChanged: (index) => _selectedIndex.value = index,
      controller: _pageController,
      children: _buildPages(),
    );
  }

  List<Widget> _buildPages() {
    return [
      HomeScreen(
        scrollController: _homeScrollController,
        onNavigateToMarket: () => _updateSelectedIndex(1),
      ),
      BlocProvider<MarketCubit>(
        create: (context) => getIt<MarketCubit>(),
        child: MarketSectionScreen(scrollController: _marketScrollController),
      ),
      const MentorListScreen(),
      const CommunityScreen(),
      const ProfileScreen(),
    ];
  }

  Widget _buildBottomNavBar(int selectedValue) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30.r)),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 0,
            blurRadius: 2,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30.r)),
        child: BottomAppBar(
          padding: EdgeInsets.zero,
          child: SizedBox(
            height: 65.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: _buildNavButtons(selectedValue),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildNavButtons(int selectedValue) {
    return List.generate(
      _bottomNavItems.length,
      (index) => BottomNavButton(
        item: _bottomNavItems[index],
        isSelected: selectedValue == index,
        onTap: () => _handleTabSelection(index),
      ),
    );
  }
}

// Bottom Navigation Button Widget
class BottomNavButton extends StatelessWidget {
  final BottomNavItem item;
  final bool isSelected;
  final VoidCallback onTap;

  const BottomNavButton({
    super.key,
    required this.item,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 0.18.sw,
        height: 65.h,
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSelectionIndicator(context),
            6.verticalSpace,
            _buildIcon(context),
            6.verticalSpace,
            _buildLabel(context),
            6.verticalSpace,
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionIndicator(BuildContext context) {
    return Container(
      width: 30.w,
      height: 3.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        color: isSelected
            ? myColorScheme(context).primaryColor
            : Colors.transparent,
      ),
    );
  }

  Widget _buildIcon(BuildContext context) {
    return SvgPicture.asset(
      isSelected ? item.selectedIcon : item.icon,
      height: 24.r,
      width: 24.r,
      color: isSelected
          ? myColorScheme(context).primaryColor
          : myColorScheme(context).greyColor3,
    ).animatedSwitch();
  }

  Widget _buildLabel(BuildContext context) {
    return Text(
      item.label.tr(),
      style:
          (isSelected ? FontPalette.medium12 : FontPalette.normal12).copyWith(
        color: isSelected
            ? myColorScheme(context).primaryColor
            : myColorScheme(context).secondaryVar1,
      ),
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
    );
  }
}

// Bottom Navigation Item Model
class BottomNavItem {
  final String icon;
  final String selectedIcon;
  final String label;
  final bool requiresAuth;

  const BottomNavItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.requiresAuth = false,
  });
}
