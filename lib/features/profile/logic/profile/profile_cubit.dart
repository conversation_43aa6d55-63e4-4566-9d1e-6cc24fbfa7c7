import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/features/profile/domain/models/add_wallet_model/add_wallet_model.dart';
import 'package:sf_app_v2/features/profile/domain/models/email_check/email_code_check.dart';
import 'package:sf_app_v2/features/profile/domain/models/profile/profile_info.dart';
import 'package:sf_app_v2/features/profile/domain/repository/profile_repository.dart';

import '../../../../core/common_function.dart';
import '../../../../core/config/refactored_app_config.dart';
import '../../../../core/dependency_injection/injectable.dart';
part 'profile_state.dart';

@injectable
class ProfileCubit extends Cubit<ProfileState> {
  final ProfileRepository _profileService;
  ProfileCubit(this._profileService) : super(const ProfileState());

  Future<void> getProfileInfo() async {
    emit(state.copyWith(infoFetchStatus: DataStatus.loading));
    try {
      final result = await _profileService.requestProfileInfo();
      if (result.data != null) {
        generateLink(
          invitationCode: result.data?.infoData?.invitationCode ?? '',
        );
        emit(
          state.copyWith(
            infoData: result.data?.infoData,
            infoFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            infoFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          infoFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> addWallet({
    required String address,
    required String name,
    required String type,
  }) async {
    emit(state.copyWith(addWalletFetchStatus: DataStatus.loading));
    try {
      final result = await _profileService.addWallet(
        address: address,
        name: name,
        type: type,
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            addWalletFetchStatus: DataStatus.success,
            addWalletData: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            addWalletFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          addWalletFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> deleteWalletAddress({
    required int addressId,
  }) async {
    emit(state.copyWith(deleteWalletFetchStatus: DataStatus.loading));
    try {
      final result = await _profileService.deleteWalletAddress(
        addressId: addressId,
      );
      if (result.data == true) {
        emit(
          state.copyWith(
            deleteWalletFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            deleteWalletFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          deleteWalletFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> sendEmailCode() async {
    emit(state.copyWith(timer: 60, emailSendStatus: DataStatus.loading));
    try {
      final result = await _profileService.requestEmailSend();
      if (result.data != null) {
        emit(
          state.copyWith(
            emailData: result.data,
            showTimer: true,
            emailSendStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            emailSendStatus: DataStatus.failed,
            showTimer: false,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          emailSendStatus: DataStatus.failed,
          showTimer: false,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> checkEmailCode({required String code}) async {
    emit(state.copyWith(emailCheckStatus: DataStatus.loading));
    try {
      final result = await _profileService.checkEmailCode(
        code: code,
        nonce: state.emailData?.data.nonce ?? '',
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            checkData: result.data?.data,
            emailCheckStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            emailCheckStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          emailCheckStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> changeWalletPassword({
    required String nonce,
    required String payPassword,
    required String confirmPayPassword,
  }) async {
    emit(
      state.copyWith(
        passChangeStatus: DataStatus.loading,
        showTimer: false,
        timer: 0,
      ),
    );
    try {
      final result = await _profileService.changeWalletPassword(
        nonce: nonce,
        payPassword: payPassword,
        confirmPayPassword: confirmPayPassword,
      );
      if (result.data != null) {
        emit(state.copyWith(passChangeStatus: DataStatus.success));
      } else {
        emit(
          state.copyWith(
            passChangeStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          passChangeStatus: DataStatus.failed,
          showTimer: false,
          timer: 0,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> changeLoginPassword({
    required String nonce,
    required String password,
    required String confirmPassword,
  }) async {
    emit(
      state.copyWith(
        loginPasswordChangeStatus: DataStatus.loading,
        showTimer: false,
        timer: 0,
      ),
    );
    try {
      final result = await _profileService.changeLoginPassword(
        nonce: nonce,
        payPassword: password,
        confirmPayPassword: confirmPassword,
      );
      if (result.data != null) {
        emit(state.copyWith(loginPasswordChangeStatus: DataStatus.success));
      } else {
        emit(
          state.copyWith(
            loginPasswordChangeStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          loginPasswordChangeStatus: DataStatus.failed,
          showTimer: false,
          timer: 0,
          error: e.toString(),
        ),
      );
    }
  }

  void manageOnCopiedInvitation() {
    emit(state.copyWith(onCopiedInvitation: true));
    Future.delayed(const Duration(milliseconds: 400), () {
      emit(state.copyWith(onCopiedInvitation: false));
    });

    Clipboard.setData(ClipboardData(text: state.firebaseShortUrl!));
    CommonFunctions().showFlutterToast(StringConstants.copiedClipboard.tr());
  }

  resetTimer() {
    emit(state.copyWith(showTimer: false, timer: 0));
  }

  manageTimer() {
    emit(state.copyWith(showTimer: false));
  }

  setIsSentOnce({required bool isSentOnce}) {
    emit(state.copyWith(isSentOnce: isSentOnce));
  }

  setEmailSendStatus({required DataStatus emailSendStatus}) {
    emit(state.copyWith(emailSendStatus: emailSendStatus));
  }

  void generateLink({required String invitationCode}) {
    var link =
        "${getIt<RefactoredAppConfig>().appUrl}auth/signup?invitationCode=$invitationCode";
    emit(state.copyWith(firebaseShortUrl: link));
  }

  void manageOnCopiedInvitationCode() {
    emit(state.copyWith(onCopiedCode: true));
    Future.delayed(const Duration(milliseconds: 400), () {
      emit(state.copyWith(onCopiedCode: false));
    });

    Clipboard.setData(
        ClipboardData(text: state.infoData?.invitationCode ?? ''));

    CommonFunctions().showFlutterToast(StringConstants.copiedClipboard.tr());
  }
}
