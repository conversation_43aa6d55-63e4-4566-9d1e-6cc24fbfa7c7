import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';
import 'package:sf_app_v2/core/config/app_config.dart';

import '../../../core/constants/assets.dart';
import '../../../core/models/app_config/payment_type_model.dart';
import '../../../core/validator.dart';
import '../../../core/widgets/common_appbar.dart';
import '../../../core/widgets/common_text_field.dart';
import '../../../core/widgets/custom_alert_dialog.dart';

class AddWithDrawlAddressScreen extends StatefulWidget {
  final List items;

  const AddWithDrawlAddressScreen({super.key, required this.items});

  @override
  State<AddWithDrawlAddressScreen> createState() =>
      _AddWithDrawlAddressScreenState();
}

class _AddWithDrawlAddressScreenState extends State<AddWithDrawlAddressScreen>
    with Validator {
  final networkLineTextController = TextEditingController();
  final addressNameTextController = TextEditingController();
  final withdrawalTextController = TextEditingController();
  final authCodeController = TextEditingController();
  final formGlobalKey = GlobalKey<FormState>();
  PaymentTypeModel? selectedPaymentType;
  List<String> nameList = [];
  List<String> addressList = [];

  @override
  void initState() {
    super.initState();
    _initializeLists();
    _initializePaymentTypes();
  }

  void _initializeLists() {
    for (var item in widget.items) {
      nameList.add(item.name);
      addressList.add(item.address);
    }
  }

  void _initializePaymentTypes() =>
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final defaultPaymentType =
            AppConfig.getStaticPaymentTypesInAddWithdrawAddress();
        if (defaultPaymentType.isNotEmpty) {
          setState(() => selectedPaymentType = defaultPaymentType.first);
        }
      });

  bool _isErcNetwork() {
    if (selectedPaymentType == null) return true; // Default to ERC20
    return selectedPaymentType!.code.contains('ERC20');
  }


  void _showPaymentTypeBottomSheet(
      List<PaymentTypeModel> availablePaymentTypes) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PaymentTypeSelectionBottomSheet(
        availablePaymentTypes: availablePaymentTypes,
        selectedPaymentType: selectedPaymentType,
        onPaymentTypeSelected: (paymentType) {
          setState(() {
            selectedPaymentType = paymentType;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Form(
          key: formGlobalKey,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 45.0.h),
                child: CommonAppBarWidget(
                  buildContext: context,
                  enableNavBack: true,
                  onBackPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              18.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: Column(
                  children: [
                    Builder(builder: (context) {
                      final availablePaymentTypes =
                          AppConfig.getStaticPaymentTypesInAddWithdrawAddress();

                      // Set default payment type if not set and available
                      if (selectedPaymentType == null &&
                          availablePaymentTypes.isNotEmpty) {
                        WidgetsBinding.instance.addPostFrameCallback((_) =>
                            setState(() => selectedPaymentType =
                                availablePaymentTypes.first));
                      }

                      final selectedConfig = availablePaymentTypes.firstWhere(
                        (config) => config.code == selectedPaymentType!.code,
                      );

                      return InkWell(
                        onTap: () =>
                            _showPaymentTypeBottomSheet(availablePaymentTypes),
                        child: Stack(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: 10.0.h),
                              child: Container(
                                height: 47.h,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    width: 1.w,
                                    color:
                                        myColorScheme(context).primaryColor ??
                                            ColorPalette.primaryVar4,
                                  ),
                                  borderRadius: BorderRadius.circular(5.r),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      left: 24.0.w, right: 10.w),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(
                                        selectedConfig.icon,
                                        height: 27.h,
                                        width: 27.w,
                                      ),
                                      Expanded(
                                        child: Row(
                                          children: [
                                            10.horizontalSpace,
                                            Text(
                                              selectedConfig.name,
                                              style: selectedConfig
                                                          .networkType ==
                                                      PaymentType.ERC20
                                                  ? FontPalette.medium15
                                                  : FontPalette.medium15
                                                      .copyWith(
                                                      color: HexColor(
                                                        ColorPalette.hexColor1,
                                                      ),
                                                    ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SvgPicture.asset(
                                        Assets.arrowDown,
                                        color:
                                            myColorScheme(context).titleColor,
                                        height: 7.5.h,
                                        width: 15.w,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                              child: Container(
                                color: myColorScheme(context).backgroundColor,
                                child: Padding(
                                  padding:
                                      const EdgeInsets.fromLTRB(5, 0, 5, 0).r,
                                  child: Text(
                                    StringConstants.networkLineLabelText.tr(),
                                    style: FontPalette.normal14.copyWith(
                                      color:
                                          myColorScheme(context).primaryColor ??
                                              ColorPalette.primaryVar4,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                    35.verticalSpace,
                    CommonTextField(
                      maxLength: 50,
                      textInputType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      labelText: StringConstants.addressTextLabelText.tr(),
                      hintText: StringConstants.addressTextHintText.tr(),
                      controller: addressNameTextController,
                      validator: (_) => validateAddWalletCheck(
                        addressNameTextController.text,
                        nameList,
                        msg: StringConstants.walletNameExists.tr(),
                        type: 'name',
                      ),
                      onChanged: (_) {},
                    ),
                    35.verticalSpace,
                    CommonTextField(
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      textInputType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      labelText: StringConstants.withdrawAddressLabelText.tr(),
                      hintText: StringConstants.withdrawAddressHintText.tr(),
                      controller: withdrawalTextController,
                      validator: (_) => validateAddWalletCheck(
                        isErc: _isErcNetwork(),
                        withdrawalTextController.text,
                        addressList,
                        msg: StringConstants.walletAddressExists.tr(),
                        type: 'address',
                      ),
                      onChanged: (_) {},
                    ),
                    35.verticalSpace,
                    Text(
                      StringConstants.addAddressWarning.tr(),
                      style:
                          FontPalette.normal11.copyWith(letterSpacing: 0.22.sp),
                    ),
                    23.verticalSpace,
                    BlocConsumer<ProfileCubit, ProfileState>(
                      listenWhen: (previous, current) =>
                          previous.addWalletFetchStatus !=
                          current.addWalletFetchStatus,
                      listener: (context, state) {
                        if (state.addWalletFetchStatus == DataStatus.success) {
                          CommonFunctions.showDialogPopUp(
                            context,
                            CustomAlertDialog(
                              title: StringConstants.added.tr(),
                              message: StringConstants.successfully.tr(),
                              actionButtonText: StringConstants.ok.tr(),
                              buttonBackGroundColor: ColorPalette.primaryVar1,
                              onActionButtonPressed: () async {
                                context.read<ProfileCubit>().getProfileInfo();
                                Navigator.pop(context);
                                Navigator.of(context).pop();
                              },
                              headerImage: Assets.alertSuccess,
                              isLoading: false,
                              messageTextStyle: FontPalette.semiBold30
                                  .copyWith(fontSize: 39.sp),
                            ),
                          );

                          authCodeController.clear();
                        }
                      },
                      builder: (context, state) {
                        return CustomButton(
                          width: 364.w,
                          height: 54.h,
                          borderRadiusUser: 27,
                          isLoading:
                              state.addWalletFetchStatus == DataStatus.loading,
                          label: StringConstants.submit.tr(),
                          isOutlined: false,
                          onPressed: () async {
                            FocusScope.of(context).unfocus();
                            if (formGlobalKey.currentState!.validate()) {
                              await context.read<ProfileCubit>().addWallet(
                                    address: withdrawalTextController.text,
                                    name: addressNameTextController.text,
                                    type: selectedPaymentType!.code,
                                  );
                            }
                          },
                        );
                      },
                    ),
                    12.verticalSpace,
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PaymentTypeSelectionBottomSheet extends StatelessWidget {
  final List<PaymentTypeModel> availablePaymentTypes;
  final PaymentTypeModel? selectedPaymentType;
  final Function(PaymentTypeModel) onPaymentTypeSelected;

  const PaymentTypeSelectionBottomSheet({
    super.key,
    required this.availablePaymentTypes,
    required this.onPaymentTypeSelected,
    this.selectedPaymentType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          _buildDivider(context),
          _buildPaymentTypeList(context),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            StringConstants.networkLineLabelText.tr(),
            style: FontPalette.bold18.copyWith(
              color: myColorScheme(context).titleColor,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.close,
              color: myColorScheme(context).titleColor,
              size: 24.r,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      height: 1.h,
      color: myColorScheme(context).titleColor?.withValues(alpha: 0.1),
    );
  }

  Widget _buildPaymentTypeList(BuildContext context) {
    return Flexible(
      child: ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: availablePaymentTypes.length,
        separatorBuilder: (context, index) => Divider(
          height: 1.h,
          color: myColorScheme(context).titleColor?.withValues(alpha: 0.05),
        ),
        itemBuilder: (context, index) {
          final paymentType = availablePaymentTypes[index];
          final isSelected = selectedPaymentType?.code == paymentType.code;

          return InkWell(
            onTap: () {
              onPaymentTypeSelected(paymentType);
              Navigator.pop(context);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
              color: isSelected
                  ? myColorScheme(context).primaryColor?.withValues(alpha: 0.1)
                  : Colors.transparent,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    paymentType.icon,
                    width: 30.w,
                    height: 30.w,
                  ),
                  12.horizontalSpace,
                  Expanded(
                    child: Text(
                      paymentType.name,
                      style: FontPalette.medium16.copyWith(
                        color: isSelected
                            ? myColorScheme(context).primaryColor
                            : myColorScheme(context).titleColor,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: myColorScheme(context).primaryColor,
                      size: 20.r,
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
