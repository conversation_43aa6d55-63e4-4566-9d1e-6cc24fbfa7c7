import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sf_app_v2/core/api/network/models/user.dart';
import 'package:sf_app_v2/core/api/network/route_arguments/common_auth_verification_arguments.dart';
import 'package:sf_app_v2/core/config/refactored_app_config.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/shared/logic/app_data/app_data_cubit.dart';
import 'package:sf_app_v2/core/shared/logic/theme/theme_cubit.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_user_name_header.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/profile/domain/models/profile/profile_info.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';
import 'package:sf_app_v2/features/profile/widgets/list_tile_widget.dart';
import 'package:sf_app_v2/features/profile/widgets/wallet_address_box.dart';

import '../../../core/common_function.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/utils/mixin/animation.dart';
import '../../../core/utils/shared_preference_helper.dart';
import '../../../core/validator.dart';
import '../../../core/widgets/common_shimmer.dart';
import '../../../core/widgets/common_wallet_slider_item.dart';
import '../../../core/widgets/custom_alert_dialog.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/locale_aware_widget.dart';
import '../../auth/account_info/domain/models/status/status_model.dart';
import '../../home/<USER>/models/balance/balance_model.dart';
import '../../home/<USER>/home/<USER>';
import '../../home/<USER>/collection_drawers.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with Validator, StaggeredAnimation {
  final _loginLinkController = TextEditingController();
  final _invitationLinkController = TextEditingController();
  final _invitationCodeController = TextEditingController();

  @override
  void dispose() {
    _loginLinkController.dispose();
    _invitationLinkController.dispose();
    _invitationCodeController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    context.read<HomeCubit>().getBalance();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: AnimationLimiter(
        child: RefreshIndicator(
          onRefresh: () async => context.read<HomeCubit>().getBalance(),
          child: LocaleAwareWidget(builder: (context) {
            return ListView(
              key: ValueKey('profileContent_${context.locale.toString()}'),
              shrinkWrap: true,
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                _buildHeader(),
                15.verticalSpace,
                _buildProfileContent(),
              ],
            );
          }),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(0),
      child: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        elevation: 0,
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
    );
  }

  Widget _buildHeader() {
    return BlocSelector<AppDataCubit, AppDataState, User?>(
      selector: (state) => state.userData,
      builder: (context, user) {
        final id =
            context.read<ProfileCubit>().state.infoData?.userId.toString() ??
                '';
        return CommonUserNameHeader(
          userName: (user?.email?.isNotEmpty == true
                  ? user?.email
                  : user?.username) ??
              StringConstants.user.tr(),
          userId: id,
          icon1: CommonFunctions.getIconFromLevel(user?.level ?? 0),
          onPressedIcon1: null,
          onSupport: () => Navigator.pushNamed(context, routeSupportScreen),
          onNotifications: () =>
              Navigator.pushNamed(context, routeNotificationListScreen),
        );
      },
    );
  }

  Widget _buildProfileContent() {
    return Form(
      child: Builder(builder: (context) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: staggeredAnimation(
            children: [
              WalletSection(
                isSignedIn: context.read<HomeCubit>().state.balanceData != null,
              ),
              20.verticalSpace,
              const CustomCollectionButtonDrawer(isLoading: false),
              _buildLinkSection(),
              10.verticalSpace,
              _buildChatButton(),
              10.verticalSpace,
              const ThemeSwitch(),
              _buildSecuritySection(),
              10.verticalSpace,
              _buildLogoutButton(),
              if (getIt<RefactoredAppConfig>().showDebugVersionTag) ...[
                _buildVersionText(),
                31.verticalSpace,
              ]
            ],
          ),
        );
      }),
    );
  }

  Widget _buildChatButton() {
    return Center(
      child: CustomButton(
        width: 364.w,
        height: 54.h,
        label: StringConstants.chat.tr(),
        onPressed: () =>
            Navigator.pushNamed(context, routeCustomerSupportScreen),
      ),
    );
  }

  Widget _buildLinkSection() {
    return BlocSelector<ProfileCubit, ProfileState,
        (DataStatus, InfoData?, bool, String?)>(
      selector: (state) => (
        state.infoFetchStatus,
        state.infoData,
        state.onCopiedInvitation,
        state.firebaseShortUrl,
      ),
      builder: (_, data) => _buildLinkContent(data),
    );
  }

  Widget _buildLinkContent((DataStatus, InfoData?, bool, String?) data) {
    switch (data.$1) {
      case DataStatus.success:
        if (data.$2 == null) return _buildEmptyState();
        return _buildSuccessContent(data);
      case DataStatus.loading:
        return _buildSuccessContent(data);
      default:
        return _buildEmptyState();
    }
  }

  Widget _buildSuccessContent((DataStatus, InfoData?, bool, String?) data) {
    return Column(
      children: [
        35.verticalSpace,
        _buildWalletAddress(data.$2 ?? const InfoData(), data.$1),
      ],
    );
  }

  Widget _buildWalletAddress(InfoData info, DataStatus status) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 33.w),
      child: WalletAddressBox(
        items: info.expendAddresses,
        isLoading: status == DataStatus.loading,
        onPressed: () => Navigator.pushNamed(
          context,
          routeAddAddressScreen,
          arguments: {'items': info.expendAddresses},
        ),
        onDeletePressed: (addressId) {
          context
              .read<ProfileCubit>()
              .deleteWalletAddress(addressId: addressId);
        },
      ),
    );
  }

  Widget _buildSecuritySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 33.w),
          child: Text(
            StringConstants.securitySettings.tr(),
            style: FontPalette.semiBold16,
          ),
        ),
        8.verticalSpace,
        AccountOptions(context: context),
        15.verticalSpace,
      ],
    );
  }

  Widget _buildLogoutButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 36.w),
      child: CustomButton(
        width: 364.w,
        height: 54.h,
        label: StringConstants.logout.tr(),
        isOutlined: false,
        onPressed: _showLogoutDialog,
      ),
    );
  }

  Future<void> _showLogoutDialog() async {
    final confirmed = await _showPlatformSpecificDialog();
    if (confirmed == true) {
      await _performLogout();
    }
  }

  Future<bool?> _showPlatformSpecificDialog() {
    return Platform.isAndroid
        ? showDialog<bool>(
            context: context,
            builder: (context) => _buildAndroidDialog(),
          )
        : showCupertinoDialog<bool>(
            context: context,
            builder: (context) => _buildIOSDialog(),
          );
  }

  Widget _buildAndroidDialog() {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      title: Text(
        StringConstants.logoutConfirmation.tr(),
        style: FontPalette.bold20,
      ),
      actions: _buildDialogActions(),
    );
  }

  Widget _buildIOSDialog() {
    return CupertinoAlertDialog(
      title: Text(StringConstants.logoutConfirmation.tr()),
      actions: _buildDialogActions(),
    );
  }

  List<Widget> _buildDialogActions() {
    return [
      TextButton(
        child: Text(
          StringConstants.cancel.tr(),
          style: FontPalette.medium15.copyWith(
            color: myColorScheme(context).primaryColor,
          ),
        ),
        onPressed: () => Navigator.of(context).pop(false),
      ),
      TextButton(
        child: Text(
          StringConstants.logout.tr(),
          style: FontPalette.bold15.copyWith(
            color: myColorScheme(context).primaryColor,
          ),
        ),
        onPressed: () => Navigator.of(context).pop(true),
      ),
    ];
  }

  Future<void> _performLogout() async {
    await Future.delayed(
      const Duration(milliseconds: 10),
      () {
        if (mounted) {
          CommonFunctions.logoutUser(context, isNavToMain: true);
        }
      },
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      width: 0.2.sw,
      height: 45.h,
    );
  }

  Widget _buildVersionText() {
    return FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Center(
            child: Text(
              'v${snapshot.data?.version ?? ''}_${snapshot.data?.buildNumber ?? ''}',
              style: FontPalette.medium12.copyWith(
                color: myColorScheme(context).titleColor,
              ),
            ),
          ),
        );
      },
    );
  }
}

class AccountOptions extends StatelessWidget {
  final BuildContext context;

  const AccountOptions({
    super.key,
    required this.context,
  });

  void _navigateToAuthScreen(String routeName) {
    context
        .read<ProfileCubit>()
        .setEmailSendStatus(emailSendStatus: DataStatus.idle);
    Navigator.pushNamed(
      context,
      routeAuthVerificationScreen,
      arguments: CommonAuthVerificationArguments(routeName: routeName),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountInfoCubit, AccountInfoState>(
      builder: (context, state) {
        final isAuthenticated = state.isAccountVerified;
        return isAuthenticated
            ? _buildCompletionOptions()
            : _buildNotCompletionOptions();
      },
    );
  }

  Widget _buildNotCompletionOptions() {
    return Column(
      children: [
        ListTileWidget(
          title: StringConstants.changeLoginPassword.tr(),
          onPressed: () => _navigateToAuthScreen(routeChangeLoginPassword),
        ),
        4.verticalSpace,
        const AccountInfo(),
        4.verticalSpace,
        _buildTermsAndPrivacyTiles(),
      ],
    );
  }

  Widget _buildCompletionOptions() {
    return Column(
      children: [
        ListTileWidget(
          title: StringConstants.changeLoginPassword.tr(),
          onPressed: () => context.handleSignedInAction(
            onTap: () => _navigateToAuthScreen(routeChangeLoginPassword),
          ),
        ),
        4.verticalSpace,
        ListTileWidget(
          title: StringConstants.changeWalletPassword.tr(),
          onPressed: () => context.handleSignedInAction(
            onTap: () => _navigateToAuthScreen(routeChangePasswordScreen),
          ),
        ),
        4.verticalSpace,
        _buildAuthenticationTile(),
        4.verticalSpace,
        _buildTermsAndPrivacyTiles(),
      ],
    );
  }

  Widget _buildAuthenticationTile() {
    return ListTileWidget(
      title: StringConstants.authentication.tr(),
      onPressed: () => context.handleSignedInAction(
        onTap: () {
          final authenticated =
              SharedPreferenceHelper().isAccountStatusLogged() ?? false;
          if (authenticated) {
            _showCertificationCompletedDialog();
          } else {
            Navigator.pushNamed(context, routeAccountInformationScreen);
          }
        },
      ),
    );
  }

  void _showCertificationCompletedDialog() {
    CommonFunctions.showDialogPopUp(
      context,
      CustomAlertDialog(
        message: StringConstants.certificationCompleted.tr(),
        buttonBackGroundColor: ColorPalette.primaryVar1,
        actionButtonText: StringConstants.ok.tr(),
        onActionButtonPressed: () => Navigator.pop(context),
        headerImage: Assets.alertSuccess,
        isLoading: false,
        messageTextStyle: FontPalette.medium20,
      ),
      barrierDismissible: false,
    );
  }

  Widget _buildTermsAndPrivacyTiles() {
    return Column(
      children: [
        ListTileWidget(
          title: StringConstants.termsAndConditions.tr(),
          onPressed: () =>
              Navigator.pushNamed(context, routeTermsAndConditionScreen),
        ),
        4.verticalSpace,
        ListTileWidget(
          title: StringConstants.privacyPolicy.tr(),
          onPressed: () =>
              Navigator.pushNamed(context, routePrivacyPolicyScreen),
        ),
        if (getIt<RefactoredAppConfig>().showBenefitRules) ...[
          4.verticalSpace,
          ListTileWidget(
            title: StringConstants.benefitRules.tr(),
            onPressed: () =>
                Navigator.pushNamed(context, routeBenefitRulesScreen),
          ),
        ]
      ],
    );
  }
}

class ThemeSwitch extends StatelessWidget {
  const ThemeSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
      selector: (state) => state.themeMode,
      builder: (context, themeMode) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: ListTile(
            title: Text(
              StringConstants.darkMode.tr(),
              style: FontPalette.semiBold14.copyWith(
                color: myColorScheme(context).titleColor,
              ),
            ),
            trailing: Switch.adaptive(
              value: themeMode == ThemeMode.dark,
              activeColor: myColorScheme(context).primaryColor,
              onChanged: (value) =>
                  context.read<ThemeCubit>().toggleTheme(value),
            ),
          ),
        );
      },
    );
  }
}

class AccountInfo extends StatelessWidget {
  const AccountInfo({super.key});

  String? _getTileText({
    required int status,
    required AccountInfoType type,
    required BuildContext context,
  }) {
    if (status == 1 || status == 2) {
      switch (type) {
        case AccountInfoType.identity:
          return StringConstants.changeIdentity.tr();
        case AccountInfoType.googleToken:
          return StringConstants.changeGoogleAuthentication.tr();
        case AccountInfoType.mobileNo:
          return StringConstants.changeNumber.tr();
        case AccountInfoType.wallet:
          return StringConstants.changeWalletPassword.tr();
      }
    }
    return null;
  }

  void _navigateScreen(BuildContext context, AccountInfoType type) {
    context
        .read<ProfileCubit>()
        .setEmailSendStatus(emailSendStatus: DataStatus.idle);

    switch (type) {
      case AccountInfoType.wallet:
        Navigator.pushNamed(
          context,
          routeAuthVerificationScreen,
          arguments: CommonAuthVerificationArguments(
              routeName: routeChangePasswordScreen),
        );
      case AccountInfoType.googleToken:
        Navigator.pushNamed(context, routeModifyAuthCodeScreen);
      default:
        Navigator.pushNamed(context, routeAccountInformationScreen);
    }
  }

  bool _showInfo(StatusData? status) {
    return !(status?.identity == 1 && status?.wallet == 1);
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<AccountInfoCubit, AccountInfoState, StatusData?>(
      selector: (state) => state.statusData,
      builder: (context, status) {
        final tiles = [
          _buildTile(
            context,
            status: status?.wallet ?? 0,
            type: AccountInfoType.wallet,
          ),
        ].whereType<Widget>().toList();

        return Column(
          children: [
            ...tiles,
            if (_showInfo(status))
              ListTileWidget(
                titleStyle: FontPalette.normal12.copyWith(
                  color: myColorScheme(context).tagRed,
                ),
                preffix: Icon(
                  Icons.info_outline_rounded,
                  size: 20.sp,
                  color: myColorScheme(context).tagRed,
                ),
                suffix: Padding(
                  padding: const EdgeInsets.only(right: 36.0),
                  child: SvgPicture.asset(
                    Assets.arrowForward,
                    color: myColorScheme(context).tagRed,
                  ),
                ),
                title: StringConstants.addAccountInfo.tr(),
                onPressed: () => Navigator.pushNamed(
                  context,
                  routeAccountInformationScreen,
                ),
              ),
          ],
        );
      },
    );
  }

  Widget? _buildTile(
    BuildContext context, {
    required int status,
    required AccountInfoType type,
  }) {
    final title = _getTileText(status: status, type: type, context: context);
    if (title != null) {
      return Column(
        children: [
          ListTileWidget(
            title: title,
            onPressed: () => _navigateScreen(context, type),
          ),
          4.verticalSpace,
        ],
      );
    }
    return null;
  }
}

class WalletSection extends StatelessWidget {
  final bool isSignedIn;

  const WalletSection({
    super.key,
    required this.isSignedIn,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        if (state.balanceData != null || !isSignedIn) {
          return WalletSlider(
            balance: state.balanceData?.data ??
                const BalanceData(
                  cash: '0',
                  community: CommunityBalance(
                    availableBalance: 0,
                    lockedBalance: 0,
                    totalAmount: 0,
                  ),
                ),
          );
        }
        return const WalletShimmer();
      },
    );
  }
}

class WalletSlider extends StatelessWidget {
  final BalanceData balance;

  const WalletSlider({
    super.key,
    required this.balance,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        children: [
          CommonWalletSliderItem(
            width: 1.sw,
            height: 121.h,
            balance: balance,
            title: StringConstants.fundingWallet,
          ),
          if (getIt<RefactoredAppConfig>().showTradingWallet) ...[
            15.verticalSpace,
            CommonWalletSliderItem(
              width: 1.sw,
              height: 121.h,
              balance: balance,
              isCommunity: true,
              title: StringConstants.tradingWallet,
            ),
          ],
        ],
      ),
    );
  }
}

class WalletShimmer extends StatelessWidget {
  const WalletShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          children: [
            CommonShimmer(
              br: 20.r,
              width: 1.sw - 30.w,
              height: 205.h,
            ),
            10.verticalSpace,
            Row(
              children: [
                for (int i = 0; i < 3; i++) ...[
                  CommonShimmer(
                    br: 20.r,
                    width: 30.w,
                    height: 8.h,
                    color: ColorPalette.shimmerColor,
                  ),
                  if (i < 2) 6.horizontalSpace,
                ],
              ],
            ),
          ],
        ),
      ],
    );
  }
}
