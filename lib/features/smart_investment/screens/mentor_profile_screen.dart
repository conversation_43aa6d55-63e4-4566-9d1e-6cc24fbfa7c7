import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

import '../../../core/api/network/network_helper.dart';
import '../../../core/common_function.dart';
import '../../../core/config/refactored_app_config.dart';
import '../../../core/dependency_injection/injectable.dart';
import '../../../core/config/purchase_percentage_config.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/models/cm_model/cm_product_model.dart';
import '../../../core/routes/routes.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_appbar.dart';
import '../../../core/widgets/common_shimmer.dart';
// import '../../chat_v2/models/cm_product_model.dart';
import '../../home/<USER>/home/<USER>';
import '../../profile/logic/profile/profile_cubit.dart';
import '../domain/models/investment_products/investment_products.dart';
import '../domain/models/purchase_percentage/purchase_percentage.dart';
import '../domain/models/mentor/mentor_model.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';
import '../widgets/purchase_bottom_sheet.dart';
import '../widgets/purchased_products_widget.dart';
// import 'package:timezone/timezone.dart' as tz;

class MentorProfileScreen extends StatefulWidget {
  final Mentor mentor;
  final CMProductModel? data;

  const MentorProfileScreen({
    super.key,
    required this.mentor,
    this.data,
  });

  @override
  State<MentorProfileScreen> createState() => _MentorProfileScreenState();
}

class _MentorProfileScreenState extends State<MentorProfileScreen> {
  late final TextEditingController _amountController;

  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController();
    CommonFunctions.afterInit(_initialFunction);
  }

  void _initialFunction() {
    final cubit = context.read<SmartInvestmentCubit>();
    final profileCubit = context.read<ProfileCubit>();
    final homeCubit = context.read<HomeCubit>();
    homeCubit.getBalance();
    if (getIt<RefactoredAppConfig>().showSmartInvestmentPurchasePercentage) {
      cubit.getPurchasePercentageConfig();
    }
    cubit.getProducts(
      mentorId: widget.mentor.id ?? 0,
      productId: widget.data?.productId,
    );
    profileCubit.getProfileInfo();
    cubit.getPurchasedList(mentorId: widget.mentor.id ?? 0);

    // Initialize purchase percentage to calculate initial amount
    cubit.initializePurchasePercentage();
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _handleInvestment() async {
    final int userLevel =
        context.read<ProfileCubit>().state.infoData?.userLevel ?? 0;
    if (!context.read<SmartInvestmentCubit>().state.agreementChecked) {
      CommonFunctions().showFlutterToast('Please agree to service agreement');
      return;
    }

    if (_amountController.text.isEmpty) {
      CommonFunctions().showFlutterToast('Please enter amount');
      return;
    }

    if (context.read<SmartInvestmentCubit>().validateInvestment(
          context,
          mentorLevel: widget.mentor.vipLevel ?? 0,
          userLevel: userLevel,
        )) {
      _showInvestmentSheet();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (_, result) async =>
          context.read<SmartInvestmentCubit>().clearInvestmentState(),
      child: Scaffold(
        appBar: CommonAppBar(
            buildContext: context,
            enableNavBack: true,
            titleWidget: Text(StringConstants.oneClickPurchase.tr()),
            alignCenter: true,
            actionList: [
              IconButton(
                onPressed: () => context.handleSignedInAction(
                  skipAccountCheck: true,
                  onTap: () => Navigator.pushNamed(
                      context, routeSmartInvestmentRecordScreen),
                ),
                icon: SvgPicture.asset(
                  Assets.contracts1,
                  width: 14.w,
                  height: 18.h,
                  color: Theme.of(context).iconTheme.color,
                ),
              ),
            ]),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InvestmentCard(amountController: _amountController),
                14.verticalSpace,
                ProductsSection(
                    onTap: _handleInvestment,
                    mentorLevel: widget.mentor.vipLevel),
                24.verticalSpace,
                // const Instructions(),
                // 24.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showInvestmentSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (_) => PurchaseBottomSheet(
        mentorId: widget.mentor.id ?? 0,
        amount: double.parse(context.read<SmartInvestmentCubit>().state.amount),
        productId: context
            .read<SmartInvestmentCubit>()
            .state
            .selectedProduct!
            .id
            .toString(),
        onClose: () {
          context.read<SmartInvestmentCubit>().resetInvestmentFlow();
        },
        type: InvestmentType.community,
      ),
    );
  }
}

class AutoRenewalSwitch extends StatelessWidget {
  const AutoRenewalSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<SmartInvestmentCubit, SmartInvestmentState, bool>(
      selector: (state) => state.isAutoRenewal,
      builder: (context, isAutoRenewal) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              StringConstants.automaticRenewal.tr(),
              style: FontPalette.normal14,
            ),
            8.horizontalSpace,
            Switch.adaptive(
              value: isAutoRenewal,
              onChanged: (value) {
                context.read<SmartInvestmentCubit>().setAutoRenewal(value);
              },
              activeColor: myColorScheme(context).primaryColor,
              activeTrackColor: myColorScheme(context).primaryColor,
            ),
          ],
        );
      },
    );
  }
}

class InvestmentCard extends StatelessWidget {
  final TextEditingController amountController;

  const InvestmentCard({super.key, required this.amountController});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        if (state.investmentRecordFetchStatus == DataStatus.loading) {
          return const ShimmerCard();
        }
        final product = state.selectedProduct;
        return Container(
          decoration: BoxDecoration(
            color: myColorScheme(context).primaryColor,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  right: -1.sw / 3.5,
                  bottom: 30.h,
                  child: SvgPicture.asset(
                    Assets.circles,
                    height: 230.h,
                    width: 300.w,
                    colorFilter:
                        const ColorFilter.mode(Colors.white38, BlendMode.srcIn),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    children: [
                      _buildCardRow(
                        '${StringConstants.celebrityMentor.tr()} :',
                        product?.mentor ?? '----',
                      ),
                      12.verticalSpace,
                      _buildCardRow(
                        '${StringConstants.smartInvestmentCycle.tr()} :',
                        '${(product?.cycle) ?? '0'} trading days',
                      ),
                      12.verticalSpace,
                      _buildCardRow(
                        '${StringConstants.mentorCommission.tr()} :',
                        '${(product?.commissionRate) ?? '0'}%',
                      ),
                      12.verticalSpace,
                      _buildCardRow(
                        '${StringConstants.minimumPurchaseAmount.tr()} :',
                        '${(product?.minAmount) ?? '0'}'.toCurrency(),
                      ),
                      12.verticalSpace,
                      _buildCardRow(
                        '${StringConstants.maximumPurchaseAmount.tr()} :',
                        '${(product?.maxAmount) ?? '0'}'.toCurrency(),
                      ),
                      12.verticalSpace,
                      _buildCardRow(
                        '${StringConstants.singleAmount.tr()} :',
                        '${(product?.singleAmount) ?? '0'}'.toCurrency(),
                      ),
                      12.verticalSpace,
                      _buildAmountInput(
                        amount: product?.singleAmount,
                        context: context,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCardRow(String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: FontPalette.normal14.copyWith(color: Colors.white),
        ),
        Expanded(
          child: Text(
            value,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: FontPalette.normal14.copyWith(color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildAmountInput({double? amount, BuildContext? context}) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        final cubit = context.read<SmartInvestmentCubit>();
        final amount = double.tryParse(state.amount);
        final shouldShowPercentageButtons =
            cubit.shouldShowPurchasePercentageButtons;

        if (shouldShowPercentageButtons) {
          amountController.text = amount?.toStringAsFixed(2) ?? '0';
        }

        if (state.purchasePercentageConfigFetchStatus == DataStatus.loading) {
          return _buildLoadingShimmer(context);
        }
        return _buildAmountInputLayout(
          shouldShowPercentageButtons: shouldShowPercentageButtons,
          state: state,
          context: context,
        );
      },
    );
  }

  Widget _buildLoadingShimmer(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: Shimmer(
            duration: const Duration(seconds: 3),
            interval: const Duration(milliseconds: 200),
            colorOpacity: 1,
            enabled: true,
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorPalette.shimmerColorDark
                : ColorPalette.shimmerColor,
            direction: const ShimmerDirection.fromLTRB(),
            child: Container(
              width: double.infinity,
              height: 40.h,
              color: Theme.of(context).brightness == Brightness.dark
                  ? ColorPalette.shadowColorDark
                  : ColorPalette.white.withAlpha(80),
            ),
          ),
        ),
        12.verticalSpace,
        ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: Shimmer(
            duration: const Duration(seconds: 3),
            interval: const Duration(milliseconds: 200),
            colorOpacity: 1,
            enabled: true,
            color: Theme.of(context).brightness == Brightness.dark
                ? ColorPalette.shimmerColorDark
                : ColorPalette.shimmerColor,
            direction: const ShimmerDirection.fromLTRB(),
            child: Container(
              width: double.infinity,
              height: 40.h,
              color: Theme.of(context).brightness == Brightness.dark
                  ? ColorPalette.shadowColorDark
                  : ColorPalette.white.withAlpha(80),
            ),
          ),
        ),
      ],
    );
  }

  List<TextInputFormatter> _getNumericInputFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
      TextInputFormatter.withFunction((oldValue, newValue) {
        // Prevent multiple dots
        if (newValue.text.split('.').length > 2) {
          return oldValue;
        }

        // Auto-prepend 0 when starting with dot
        if (newValue.text.startsWith('.') && newValue.text.length == 1) {
          return const TextEditingValue(
            text: '0.',
            selection: TextSelection.collapsed(offset: 2),
          );
        }

        // Prevent multiple leading zeros
        if (newValue.text.length > 1 &&
            newValue.text.startsWith('0') &&
            !newValue.text.startsWith('0.') &&
            newValue.text[1] == '0') {
          return oldValue;
        }

        // Allow only 2 digits after decimal
        if (newValue.text.contains('.')) {
          String afterDecimal = newValue.text.split('.')[1];
          if (afterDecimal.length > 2) {
            return oldValue;
          }
        }

        return newValue;
      }),
    ];
  }

  InputDecoration _getInputDecoration() {
    return InputDecoration(
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 16.h,
      ),
      labelText: StringConstants.amount.tr(),
      labelStyle: FontPalette.normal14.copyWith(
        fontWeight: FontWeight.w400,
        color: Colors.white,
      ),
      hintText: StringConstants.enterAmount.tr(),
      hintStyle: FontPalette.normal14.copyWith(
        color: Colors.white.withOpacity(0.5),
      ),
      floatingLabelBehavior: FloatingLabelBehavior.always,
      border: _buildInputBorder(),
      enabledBorder: _buildInputBorder(),
      disabledBorder: _buildInputBorder(opacity: 0.4),
      focusedBorder: _buildInputBorder(opacity: 1.0),
      filled: false,
    );
  }

  Widget _buildTextField({
    required bool shouldShowPercentageButtons,
    required BuildContext context,
  }) {
    return SizedBox(
      height: 45.h,
      width: double.infinity,
      child: TextField(
        controller: amountController,
        enabled: !shouldShowPercentageButtons,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        cursorColor: Colors.white,
        style: FontPalette.normal16.copyWith(color: Colors.white),
        inputFormatters: shouldShowPercentageButtons ? null : _getNumericInputFormatters(),
        decoration: _getInputDecoration(),
        onChanged: (value) => context.read<SmartInvestmentCubit>().setAmount(value),
      ),
    );
  }

  Widget _buildAmountInputLayout({
    required bool shouldShowPercentageButtons,
    required SmartInvestmentState state,
    required BuildContext context,
  }) => SizedBox(
        height: 45.h,
        width: double.infinity,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: _buildTextField(
                shouldShowPercentageButtons: shouldShowPercentageButtons,
                context: context,
              ),
            ),
            if (shouldShowPercentageButtons) ...[
              SizedBox(width: 10.w),
              _buildPercentageButtons(state, context)
            ]
          ],
        ),
      );

  OutlineInputBorder _buildInputBorder({double opacity = 1.0}) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(8.r),
      borderSide: BorderSide(
        color: Colors.white.withAlpha((opacity * 255).toInt()),
        width: 1.0,
      ),
    );
  }

  Widget _buildPercentageButtons(
      SmartInvestmentState state, BuildContext context) {
    return Wrap(
      runSpacing: 8.h,
      alignment: WrapAlignment.start,
      runAlignment: WrapAlignment.start,
      crossAxisAlignment: WrapCrossAlignment.start,
      children: state.purchasePercentage
          .map((percentage) => _buildPercentageButton(
                percentage: percentage,
                state: state,
                context: context,
              ))
          .toList(),
    );
  }

  Widget _buildPercentageButton({
    required PurchasePercentage percentage,
    required SmartInvestmentState state,
    required BuildContext context,
  }) {
    final isDisabled = !PurchasePercentageConfig.isEnabled(
      percentage.percentage,
      apiData: state.purchasePercentageConfigData,
    );

    return Bounceable(
      onTap: isDisabled
          ? null
          : () => context
              .read<SmartInvestmentCubit>()
              .setPurchasePercentage(percentage: percentage),
      child: Container(
        height: 40.h,
        width: 60.w,
        margin: EdgeInsets.only(left: 8.w),
        decoration: BoxDecoration(
          color: _getPercentageButtonColor(isDisabled, percentage.isSelected),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.white),
        ),
        child: Center(
          child: Text(
            '${percentage.percentage}%',
            style: FontPalette.normal12.copyWith(
              color: ColorPalette.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  Color _getPercentageButtonColor(bool isDisabled, bool isSelected) {
    if (isDisabled) {
      return Colors.white.withAlpha(50); // More faded for disabled buttons
    }
    return isSelected
        ? Colors.white
        : Colors.white.withAlpha(102); // 0.4 opacity = 102/255
  }
}

class ShimmerCard extends StatelessWidget {
  const ShimmerCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
              right: -1.sw / 3.5,
              bottom: 30.h,
              child: SvgPicture.asset(
                Assets.circles,
                height: 230.h,
                width: 300.w,
                colorFilter:
                    const ColorFilter.mode(Colors.white38, BlendMode.srcIn),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                children: [
                  CommonShimmer(
                    width: double.infinity,
                    height: 20.h,
                    br: 4.r,
                  ),
                  12.verticalSpace,
                  CommonShimmer(
                    width: double.infinity,
                    height: 20.h,
                    br: 4.r,
                  ),
                  12.verticalSpace,
                  CommonShimmer(
                    width: double.infinity,
                    height: 20.h,
                    br: 4.r,
                  ),
                  12.verticalSpace,
                  CommonShimmer(
                    width: double.infinity,
                    height: 20.h,
                    br: 4.r,
                  ),
                  12.verticalSpace,
                  CommonShimmer(
                    width: double.infinity,
                    height: 20.h,
                    br: 4.r,
                  ),
                  12.verticalSpace,
                  CommonShimmer(
                    width: double.infinity,
                    height: 20.h,
                    br: 4.r,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ProductsSection extends StatelessWidget {
  final VoidCallback onTap;
  final int? mentorLevel;

  const ProductsSection(
      {super.key, required this.onTap, required this.mentorLevel});

  @override
  Widget build(BuildContext context) {
    return buildProductsSection(context, onTap);
  }

  Widget buildProductsSection(BuildContext context, VoidCallback onTap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BlocBuilder<ProfileCubit, ProfileState>(builder: (context, state) {
          return Column(
            children: [
              _buildProductGrid(),
              8.verticalSpace,
              _buildAgreementSection(),
              // 8.verticalSpace,
              // buildTimeNoticeText(context),
              12.verticalSpace,
              if (getIt<RefactoredAppConfig>().showMentorVipLevel &&
                  mentorLevel != null &&
                  state.infoData?.userLevel != null &&
                  state.infoData!.userLevel! < mentorLevel!) ...[
                buildVipNoticeText(context, mentorLevel!),
                12.verticalSpace,
              ],
              _buildInvestButton(onTap, mentorLevel),
              8.verticalSpace,
            ],
          );
        }),
        _buildPurchasedProducts(context),
      ],
    );
  }

  Widget _buildProductGrid() {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        if (state.productListFetchStatus == DataStatus.loading) {
          return GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.only(bottom: 16.h),
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 10.h,
              crossAxisSpacing: 10.w,
              childAspectRatio: 2.9,
            ),
            itemCount: 6,
            itemBuilder: (context, index) => _buildShimmerItem(context),
          );
        }
        return GridView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.only(bottom: 16.h),
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 10.h,
            crossAxisSpacing: 10.w,
            childAspectRatio: 2.9,
          ),
          itemCount: state.productListData?.length ?? 0,
          itemBuilder: (context, index) =>
              buildProductItem(state.productListData![index]),
        );
      },
    );
  }

  Widget _buildShimmerItem(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: myColorScheme(context).shimmerColor,
      ),
      child: const CommonShimmer(),
    );
  }

  Widget buildProductItem(Product product) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        final isSelected = state.selectedProduct?.id == product.id;
        return Bounceable(
          onTap: () =>
              context.read<SmartInvestmentCubit>().selectProduct(product),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              color: isSelected
                  ? myColorScheme(context).primaryColor
                  : const Color(0xffF1F4FF),
              border: Border.all(
                color: isSelected
                    ? (myColorScheme(context).primaryColor ??
                        ColorPalette.primaryColor)
                    : ColorPalette.greyColor1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  product.name ?? '',
                  style: FontPalette.normal12.copyWith(
                    color: isSelected ? Colors.white : ColorPalette.titleColor,
                  ),
                ),
                4.verticalSpace,
                Text(
                  '${(product.minAmount ?? 0).toString().toCurrency()}-${(product.maxAmount ?? 0).toString().toCurrency()}',
                  style: FontPalette.normal12.copyWith(
                    color: isSelected ? Colors.white : ColorPalette.greyColor3,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAgreementSection() {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        final isChecked = state.agreementChecked;
        return Row(
          children: [
            SizedBox(
              height: 24.h,
              width: 24.w,
              child: Checkbox(
                value: isChecked,
                onChanged: (value) {
                  context
                      .read<SmartInvestmentCubit>()
                      .setAgreementChecked(value ?? false);
                },
                activeColor: myColorScheme(context).primaryColor,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            8.horizontalSpace,
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => context
                    .read<SmartInvestmentCubit>()
                    .setAgreementChecked(!isChecked),
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: '${StringConstants.iHaveReadAndAgreed.tr()} ',
                        style: FontPalette.normal12.copyWith(
                          color: myColorScheme(context).greyColor4,
                        ),
                      ),
                      TextSpan(
                        text: StringConstants.serviceAgreement.tr(),
                        style: FontPalette.normal12.copyWith(
                          color: ColorPalette.tagBlue,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(
                              context,
                              routeServiceAgreementScreen,
                            );
                          },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget buildTimeNoticeText(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7E6),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: const Color(0xFFE6A23C),
        ),
      ),
      child: Center(
        child: Text(
          StringConstants.buyingTimeNotice.tr(),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
          style: FontPalette.normal14.copyWith(
            color: const Color(0xFFE6A23C),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget buildVipNoticeText(BuildContext context, int vipLevel) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: const Color(0xfffffbe6),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: const Color(0xffffe58f),
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Text(
              StringConstants.vipNotice.tr(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: FontPalette.normal14.copyWith(
                color: const Color(0xfffaad14),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              StringConstants.vipNoticeDescription.tr(
                args: [vipLevel.toString()],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: FontPalette.normal12.copyWith(
                fontWeight: FontWeight.w500,
                color: const Color(0xfffaad14).withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvestButton(VoidCallback onTap, int? mentorLevel) {
    return BlocSelector<SmartInvestmentCubit, SmartInvestmentState,
        (bool?, DataStatus)>(
      selector: (state) => (
        state.agreementChecked,
        state.investmentRecordFetchStatus,
      ),
      builder: (context, data) {
        final isAgreed = data.$1;
        final status = data.$2;

        return SizedBox(
          width: double.infinity,
          child: MultiBlocListener(
            listeners: [
              BlocListener<SmartInvestmentCubit, SmartInvestmentState>(
                listenWhen: (previous, current) =>
                    previous.submitInvestmentStatus !=
                    current.submitInvestmentStatus,
                listener: (context, state) async {
                  if (state.submitInvestmentStatus == DataStatus.failed) {
                    NetworkHelper.handleMessage(
                      state.error,
                      context,
                      type: HandleTypes.customDialog,
                      snackBarType: SnackBarType.error,
                    );
                  }
                  if (state.submitInvestmentStatus == DataStatus.success) {
                    await context.read<HomeCubit>().getBalance();
                    if (context.mounted) {
                      final cubit = context.read<SmartInvestmentCubit>();
                      if (cubit.shouldShowPurchasePercentageButtons) {
                        cubit.setPurchasePercentage();
                      }
                    }
                  }
                },
              ),
            ],
            child: BlocBuilder<ProfileCubit, ProfileState>(
              builder: (context, state) {
                return ElevatedButton(
                  onPressed: _getButtonAction(context, isAgreed!, status, onTap,
                      mentorLevel, state.infoData?.userLevel),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: myColorScheme(context).primaryColor,
                    disabledBackgroundColor:
                        myColorScheme(context).primaryColor?.withAlpha(100),
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                  ),
                  child: status == DataStatus.loading
                      ? const CircularProgressIndicator.adaptive()
                      : Text(
                          StringConstants.follow.tr(),
                          style:
                              FontPalette.bold16.copyWith(color: Colors.white),
                        ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  VoidCallback? _getButtonAction(
    BuildContext context,
    bool isAgreed,
    DataStatus status,
    VoidCallback onTap,
    int? mentorLevel,
    int? userLevel,
  ) {
    // if (F.appFlavor != Flavor.cfroex) {
    //   final timeZone = switch (F.appFlavor) {
    //     Flavor.ncm => tz.getLocation('Atlantic/Bermuda'),
    //     _ => tz.getLocation('America/New_York'),
    //   };

    //   final currentTime = tz.TZDateTime.now(timeZone);
    //   if (currentTime.hour >= 14) {
    //     return null;
    //   }
    // }

    if (!isAgreed || status == DataStatus.loading) {
      return null;
    }
    if (getIt<RefactoredAppConfig>().showMentorVipLevel &&
        mentorLevel != null &&
        mentorLevel > 0 &&
        userLevel != null &&
        userLevel > 0 &&
        userLevel < mentorLevel) {
      return null;
    }

    return () => context.handleSignedInAction(
          skipAccountCheck: true,
          onTap: onTap,
        );
  }

  Widget _buildPurchasedProducts(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
        builder: (context, state) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          16.verticalSpace,
          if (state.purchasedList.isNotEmpty) ...[
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: myColorScheme(context).cardColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 8.h,
              ),
              child: Text(
                StringConstants.purchasedProducts.tr(),
                style: FontPalette.bold16
                    .copyWith(color: myColorScheme(context).titleColor),
              ),
            ),
            10.verticalSpace,
          ],
          PurchasedProductsWidget(
            purchasedList: state.purchasedList,
            status: state.purchasedListFetchStatus,
          ),
        ],
      );
    });
  }
}

class ThresholdTable extends StatelessWidget {
  const ThresholdTable({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        if (state.productListFetchStatus == DataStatus.loading) {
          return const ShimmerTable();
        }

        final products = state.productListData ?? [];
        if (products.isEmpty) return const SizedBox();

        return Table(
          border: TableBorder(
            horizontalInside: BorderSide(color: ColorPalette.greyColor2),
            verticalInside: BorderSide(color: ColorPalette.greyColor2),
          ),
          children: [
            TableRow(
              decoration: BoxDecoration(
                color: myColorScheme(context).primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.r),
                  topRight: Radius.circular(8.r),
                ),
              ),
              children: [
                _buildTableHeader(StringConstants.fundingThreshold.tr()),
                _buildTableHeader(StringConstants.transactionCycle.tr()),
                _buildTableHeader(StringConstants.tutorCommission.tr()),
              ],
            ),
            ...products.map(
              (Product product) => TableRow(
                decoration: BoxDecoration(
                  color: myColorScheme(context).tableHeaderColor,
                  borderRadius: product == products.last
                      ? BorderRadius.only(
                          bottomLeft: Radius.circular(8.r),
                          bottomRight: Radius.circular(8.r),
                        )
                      : null,
                ),
                children: [
                  _buildTableCell('${product.minAmount}-${product.maxAmount}'),
                  _buildTableCell(
                    '${product.cycle} ${StringConstants.tradingDays.tr()}',
                  ),
                  _buildTableCell('${product.commissionRate}%'),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: EdgeInsets.all(8.r),
      child: Text(
        text,
        style: FontPalette.normal14.copyWith(color: Colors.white),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 16.h),
      child: Text(
        text,
        style: FontPalette.normal12.copyWith(color: ColorPalette.greyColor4),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class ShimmerTable extends StatelessWidget {
  const ShimmerTable({super.key});

  @override
  Widget build(BuildContext context) {
    return Table(
      border: TableBorder(
        horizontalInside: BorderSide(color: ColorPalette.greyColor2),
        verticalInside: BorderSide(color: ColorPalette.greyColor2),
      ),
      children: [
        TableRow(
          decoration: BoxDecoration(
            color: ColorPalette.primaryColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8.r),
              topRight: Radius.circular(8.r),
            ),
          ),
          children: [
            _buildTableHeader(StringConstants.fundingThreshold.tr()),
            _buildTableHeader(StringConstants.transactionCycle.tr()),
            _buildTableHeader(StringConstants.tutorCommission.tr()),
          ],
        ),
        ...List.generate(
          6,
          (index) => TableRow(
            decoration: BoxDecoration(
              color: const Color(0xffF1F4FF),
              borderRadius: index == 5
                  ? BorderRadius.only(
                      bottomLeft: Radius.circular(8.r),
                      bottomRight: Radius.circular(8.r),
                    )
                  : null,
            ),
            children: [
              _buildShimmerCell(),
              _buildShimmerCell(),
              _buildShimmerCell(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: EdgeInsets.all(8.r),
      child: Text(
        text,
        style: FontPalette.normal14.copyWith(color: Colors.white),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildShimmerCell() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 16.h),
      child: CommonShimmer(
        width: double.infinity,
        height: 12.h,
        br: 4.r,
      ),
    );
  }
}

class Instructions extends StatelessWidget {
  const Instructions({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 20.w,
                child: Transform.scale(
                  scaleX: -1,
                  child: SvgPicture.asset(
                    Assets.titleArrow,
                    height: 14.h,
                    width: 14.w,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                  child: Text(
                    StringConstants.oneClickSmartInvestmentInstructions.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: myColorScheme(context).titleColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 20.w,
                child: SvgPicture.asset(
                  Assets.titleArrow,
                  height: 14.h,
                  width: 14.w,
                ),
              ),
            ],
          ),
          16.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              StringConstants.oneClickSmartInvestmentDescription.tr(),
              style: FontPalette.normal12
                  .copyWith(color: myColorScheme(context).greyColor4),
              textAlign: TextAlign.left,
            ),
          ),
          16.verticalSpace,
          const ThresholdTable(),
        ],
      ),
    );
  }
}
