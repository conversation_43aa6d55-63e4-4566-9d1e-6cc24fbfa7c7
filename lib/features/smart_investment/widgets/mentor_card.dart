import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:readmore/readmore.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/mentor/mentor_model.dart';

import '../../../core/config/app_config.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/widgets/custom_button.dart';

class MentorCard extends StatelessWidget {
  final Mentor mentor;
  final VoidCallback onFollowTap;

  const MentorCard({
    super.key,
    required this.mentor,
    required this.onFollowTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: (myColorScheme(context).primaryColor ?? Colors.blue)
                .withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: (myColorScheme(context).primaryColor ?? Colors.blue)
              .withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            _buildContent(context),
            _buildFollowButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildModernAvatar(context),
            16.horizontalSpace,
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: mentor.name ?? '',
                                    style: FontPalette.semiBold18.copyWith(
                                      color: myColorScheme(context).titleColor,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment.middle,
                                    child: Padding(
                                      padding: EdgeInsets.only(left: 8.w),
                                      child: _buildExpertBadge(context),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (AppConfig.instance.showMentorVipLevel &&
                          mentor.vipLevel != null)
                      _buildVipLevel(context),
                    ],
                  ),
                  8.verticalSpace,
                  _buildInfoChips(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernAvatar(BuildContext context) {
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: myColorScheme(context).backgroundColor,
        image: mentor.avatar?.isNotEmpty ?? false
            ? DecorationImage(
                image: NetworkImage(mentor.avatar!),
                fit: BoxFit.cover,
              )
            : null,
      ),
      child: mentor.avatar?.isNotEmpty ?? false
          ? null
          : Center(
              child: Text(
                mentor.name?.substring(0, 1).toUpperCase() ?? '',
                style: FontPalette.semiBold24.copyWith(
                  color: myColorScheme(context).primaryColor,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
    );
  }

  Widget _buildExpertBadge(BuildContext context) {
    return SvgPicture.asset(
      Assets.verified,
      width: 14.w,
      height: 14.h,
    );
  }

  Widget _buildVipLevel(BuildContext context) {
    return TweenAnimationBuilder(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Text(
              'VIP ${mentor.vipLevel}',
              style: FontPalette.semiBold14.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoChips(BuildContext context) {
    final infoItems = [
      mentor.company ?? '',
      mentor.position ?? '',
      '${StringConstants.yearsOfExperience.tr()}: ${mentor.yearsOfExperience}',
    ].where((item) => item.isNotEmpty).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: infoItems.map((info) => _buildInfoChip(context, info)).toList(),
    );
  }

  Widget _buildInfoChip(BuildContext context, String text) {
    return Text(
      text,
      style: FontPalette.normal13.copyWith(
        color: myColorScheme(context).subTitleColor2,
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDescription(context),
          20.verticalSpace,
          _buildTradeStats(context),
          5.verticalSpace,
        ],
      ),
    );
  }

  Widget _buildDescription(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ReadMoreText(
            mentor.bio ?? '',
            style: FontPalette.normal14.copyWith(
              color: myColorScheme(context).subTitleColor,
              height: 1.5,
            ),
            trimMode: TrimMode.Line,
            trimLines: 2,
            colorClickableText: myColorScheme(context).primaryColor,
            trimCollapsedText: '...${StringConstants.readMore.tr()}',
            trimExpandedText: ' ${StringConstants.readLess.tr()}',
            moreStyle: FontPalette.semiBold14.copyWith(
              color: myColorScheme(context).primaryColor,
            ),
            lessStyle: FontPalette.semiBold14.copyWith(
              color: myColorScheme(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTradeStats(BuildContext context) {
    final stats = [
      {
        'label': StringConstants.monthlyReturn.tr(),
        'value': mentor.monthlyProfit?.floor() ?? 0,
        'suffix': '%',
      },
      {
        'label': StringConstants.portfolio.tr(),
        'value': mentor.portfolio ?? 0,
        'suffix': '',
      },
      {
        'label': StringConstants.maxDrawdown.tr(),
        'value': mentor.maxDrawdown?.floor() ?? 0,
        'suffix': '%',
      },
      {
        'label': StringConstants.winRate.tr(),
        'value': mentor.winRate?.floor() ?? 0,
        'suffix': '%',
      },
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [...stats.map((stat) => _buildStatCard(context, stat))],
    );
  }

  Widget _buildStatCard(BuildContext context, Map<String, dynamic> stat) {
    return Container(
      height: 70.h,
      width: 78.w,
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor2,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            stat['label'] as String,
            style: FontPalette.normal11.copyWith(
              color: myColorScheme(context).subTitleColor,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          8.verticalSpace,
          Text(
            '${stat['value']}${stat['suffix']}',
            style: FontPalette.semiBold14.copyWith(),
          ),
        ],
      ),
    );
  }

  Widget _buildFollowButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      child: CustomButton(
        width: double.infinity,
        height: 42.h,
        label: StringConstants.smartInvestmentProducts.tr(),
        onPressed: () => context.handleSignedInAction(
          skipAccountCheck: true,
          onTap: onFollowTap,
        ),
        borderRadiusUser: 6.r,
        backgroundColor: myColorScheme(context).primaryColor,
      ),
    );
  }
}
