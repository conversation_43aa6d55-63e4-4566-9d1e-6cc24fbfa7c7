import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/utils/functions.dart';

import '../../../../../core/config/app_config.dart';
import '../../../../../core/constants/string_constants.dart';
import '../../../../../core/shared/logic/chat_button/chat_button_cubit.dart';
import '../../../../../core/theme/color_pallette.dart';
import '../../../../../core/theme/font_pallette.dart';
import '../../../../../core/theme/my_color_scheme.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/custom_num_pad.dart';
import '../../../logic/transfer/transfer_cubit.dart';

class TransferRemainingBody extends StatefulWidget {
  final TextEditingController amountController;
  final int walletIndex;
  final double walletBalance;
  final bool showError;
  final VoidCallback? onTransferPressed;

  const TransferRemainingBody({
    super.key,
    required this.amountController,
    required this.walletIndex,
    required this.walletBalance,
    required this.showError,
    this.onTransferPressed,
  });

  @override
  State<TransferRemainingBody> createState() => _TransferRemainingBodyState();
}

class _TransferRemainingBodyState extends State<TransferRemainingBody> {
  final _buttonEnabledNotifier = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  @override
  void didUpdateWidget(TransferRemainingBody oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Listen for walletIndex changes and update button state
    if (oldWidget.walletIndex != widget.walletIndex) {
      _updateButtonState();
    }
  }

  @override
  void dispose() {
    widget.amountController.removeListener(_updateButtonState);
    _buttonEnabledNotifier.dispose();
    super.dispose();
  }

  void _initializeController() {
    widget.amountController
      ..text = '0'.toCurrencyWithSymbol()
      ..addListener(_updateButtonState);
  }

  void _updateButtonState() {
    final amount = parseUSDAmount(widget.amountController.text);
    _buttonEnabledNotifier.value = amount >= 1 && amount <= widget.walletBalance;
    _validateAmount(amount);
  }

  void _validateAmount(double amount) {
    context
        .read<TransferCubit>()
        .setTransferShowError(amount > widget.walletBalance);
  }

  void _handleTransfer() {
    final amount = parseUSDAmount(widget.amountController.text);
    if (amount > widget.walletBalance) {
      context.read<TransferCubit>().setTransferShowError(true);
      return;
    }

    context.read<ChatButtonCubit>().toggleChatButton(showChatButton: false);
    context.read<TransferCubit>().setTransferShowError(false);

    if (widget.walletIndex == 1 && AppConfig.instance.showTransferPreview) {
      context.read<TransferCubit>().preview(amount: amount.toString());
    }

    widget.onTransferPressed?.call();
  }

  void _handleDelete() {
    final text = widget.amountController.text;
    if (text.isNotEmpty) {
      widget.amountController.text = text.substring(0, text.length - 1);
      _updateButtonState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildAmountDisplay(context),
        _buildNumPad(),
        14.verticalSpace,
        _buildTransferButton(),
        14.verticalSpace,
      ],
    );
  }

  Widget _buildAmountDisplay(BuildContext context) {
    return Container(
      width: 285.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100.r),
        boxShadow: _buildAmountDisplayShadows(context),
      ),
      child: TextField(
        enabled: false,
        controller: widget.amountController,
        textAlign: TextAlign.center,
        showCursor: false,
        style: FontPalette.bold40,
        keyboardType: TextInputType.none,
        decoration: const InputDecoration(border: InputBorder.none),
      ),
    );
  }

  List<BoxShadow> _buildAmountDisplayShadows(BuildContext context) {
    final colorScheme = myColorScheme(context);
    return [
      BoxShadow(
        color: colorScheme.appBarIconColor?.withValues(alpha: 0.5) ??
            Colors.black12,
      ),
      BoxShadow(
        color: colorScheme.cardColor ?? Colors.white,
        spreadRadius: -1,
        blurRadius: 6,
        offset: const Offset(0, 4),
      ),
    ];
  }

  Widget _buildNumPad() {
    return NumPad(
      controller: widget.amountController,
      delete: _handleDelete,
      onSubmit: () {},
      onChanged: () =>
          _validateAmount(parseUSDAmount(widget.amountController.text)),
      width: 1.sw,
    );
  }

  Widget _buildTransferButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: _buttonEnabledNotifier,
      builder: (context, isEnabled, _) => CustomButton(
        width: 356.w,
        height: 52.h,
        label: StringConstants.transfer.tr(),
        isOutlined: false,
        isEnabled: isEnabled,
        btnTextStyle: FontPalette.medium16.copyWith(color: ColorPalette.white),
        onPressed: _handleTransfer,
      ),
    );
  }
}
