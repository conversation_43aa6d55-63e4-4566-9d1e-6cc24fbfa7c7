import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/config/refactored_app_config.dart';
import 'package:sf_app_v2/core/config/feature_configs/payment_feature_config.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_empty_data.dart';

import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/core/widgets/custom_alert_dialog.dart';
import 'package:sf_app_v2/core/widgets/otp_timer.dart';
import 'package:sf_app_v2/features/home/<USER>/models/balance/balance_model.dart';
import 'package:sf_app_v2/features/wallet/deposit/logic/deposit/deposit_cubit.dart';
import 'package:sf_app_v2/features/wallet/deposit/screens/tips_screen.dart';
import 'package:sf_app_v2/features/wallet/deposit/widgets/deposit_screen_shimmer.dart';

import '../../../../core/api/network/network_helper.dart';
import '../../../../core/models/app_config/payment_type_model.dart';
import '../../../../core/validator.dart';
import '../../../../core/widgets/common_text_field.dart';
import '../../../../core/widgets/common_wallet_slider_item.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../home/<USER>/home/<USER>';
import '../widgets/bottom_sheet.dart';

/// A screen that allows users to deposit funds into their wallet
class DepositScreen extends StatefulWidget {
  const DepositScreen({super.key});

  @override
  State<DepositScreen> createState() => _DepositScreenState();
}

class _DepositScreenState extends State<DepositScreen> with Validator {
  final TextEditingController _addressController = TextEditingController();
  final GlobalKey<FormState> _formGlobalKey = GlobalKey<FormState>();
  final ValueNotifier<bool> _buttonEnabledNotifier = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _initialFunction();
    _showDepositInfoDialog();
    _addressController.addListener(_onTextChange);
  }

  void _onTextChange() {
    _buttonEnabledNotifier.value = _addressController.text.trim().isNotEmpty;
  }

  @override
  void dispose() {
    _addressController.removeListener(_onTextChange);
    _addressController.dispose();
    _buttonEnabledNotifier.dispose();
    super.dispose();
  }

  void _initialFunction() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<DepositCubit>();
      final paymentService = getIt<PaymentFeatureService>();
      cubit.fetchFundingBalance();

      // Handle payment type initialization based on flavor
      if (paymentService.shouldFetchWalletCoinsFromApi) {
        // For SIS flavor: fetch wallet coins first, then auto-set default payment type
        cubit.fetchWalletCoins();
      } else {
        // For non-SIS flavors: directly initialize default payment type
        cubit.setDefaultPaymentTypeNonSis();
      }
    });
  }

  void _showDepositInfoDialog() {
    CommonFunctions.afterInit(() {
      CommonFunctions.showDialogPopUp(
        context,
        CustomAlertDialog(
          actionButtonText: StringConstants.ok.tr(),
          buttonBackGroundColor: myColorScheme(context).primaryColor,
          onActionButtonPressed: () => Navigator.pop(context),
          isLoading: false,
          messageTextStyle: FontPalette.medium16.copyWith(height: 1.5),
          child: _buildDialogContent(),
        ),
        barrierDismissible: false,
      );
    });
  }

  void _showPaymentTypeBottomSheet(DepositState state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PaymentTypeSelectionBottomSheet(
        items: _buildCurrencyItems(state),
        selectedPaymentType: state.paymentType,
        onPaymentTypeSelected: (paymentType) {
          context.read<DepositCubit>().setPaymentType(paymentType);
        },
      ),
    );
  }

  Widget _buildDialogContent() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: FontPalette.medium16.copyWith(
          height: 1.5,
          color: myColorScheme(context).titleColor,
        ),
        children: [
          TextSpan(text: StringConstants.thisRechargeAddressOneTime.tr()),
          const TextSpan(text: '\n'),
          TextSpan(
            text: StringConstants.pleaseComplete.tr(),
            style: FontPalette.bold16.copyWith(
              color: myColorScheme(context).titleColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          StringConstants.deposit.tr(),
          style: FontPalette.semiBold20.copyWith(
            color: myColorScheme(context).titleColor,
          ),
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        leading: IconButton(
          padding: EdgeInsets.zero,
          onPressed: () => Navigator.pop(context),
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: myColorScheme(context).appBarIconColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
      body: BlocListener<DepositCubit, DepositState>(
        listenWhen: (previous, current) =>
            previous.walletRechargeFetchStatus !=
            current.walletRechargeFetchStatus,
        listener: (context, state) {
          if (state.walletRechargeFetchStatus == DataStatus.failed) {
            NetworkHelper.handleMessage(
              state.error,
              context,
              type: HandleTypes.customDialog,
              snackBarType: SnackBarType.error,
            );
          }
        },
        child: BlocBuilder<DepositCubit, DepositState>(
          builder: (context, state) {
            if (state.depositFetchStatus == DataStatus.loading) {
              return const DepositScreenShimmer();
            }

            if (state.depositFetchStatus == DataStatus.success) {
              return _buildSuccessView(state);
            }

            return const Center(child: Text('Failed to load data'));
          },
        ),
      ),
    );
  }

  Widget _buildSuccessView(DepositState state) {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(state),
                      Expanded(
                        child: _buildDepositForm(state),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    final cubit = context.read<DepositCubit>();
    final paymentService = getIt<PaymentFeatureService>();
    await cubit.fetchFundingBalance();

    // Handle refresh based on flavor
    if (paymentService.shouldFetchWalletCoinsFromApi) {
      // For SIS flavor: refresh wallet coins
      await cubit.fetchWalletCoins();
    } else {
      // For non-SIS flavors: refresh with current payment type or default
      final currentPaymentType = cubit.state.paymentType;
      if (currentPaymentType != null) {
        await cubit.fetchWalletRechargeAddress(currentPaymentType);
      } else {
        cubit.setDefaultPaymentTypeNonSis();
      }
    }
  }



  Widget _buildHeader(DepositState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Column(
            children: [
              _buildWalletBalance(state),
              15.verticalSpace,
              _buildCurrencyDropdown(state),
            ],
          ),
        ),
        25.verticalSpace,
      ],
    );
  }

  Widget _buildWalletBalance(DepositState state) {
    return Material(
      borderRadius: BorderRadius.circular(20).r,
      elevation: 10,
      shadowColor: myColorScheme(context).primaryColor?.withValues(alpha: 0.3),
      child: CommonWalletSliderItem(
        width: 377.w,
        height: 125.h,
        balance: BalanceData(cash: state.depositWalletData?.balance ?? '0'),
        title: StringConstants.fundingWallet,
      ),
    );
  }

  Widget _buildCurrencyDropdown(DepositState state) {
    final selectedPaymentType = state.paymentType;
    final paymentService = getIt<PaymentFeatureService>();
    final availablePaymentTypes = paymentService.getAvailablePaymentTypes(
      apiWalletCoins: state.walletCoins,
    );
    final selectedConfig = availablePaymentTypes.firstWhere(
      (config) => config.id == selectedPaymentType?.id,
      orElse: () => availablePaymentTypes.first,
    );

    return GestureDetector(
      onTap: () => _showPaymentTypeBottomSheet(state),
      child: Container(
        width: 377.w,
        height: 56.h,
        decoration: BoxDecoration(
          color: myColorScheme(context).primaryColor,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                selectedConfig.name.split(' ')[0],
                style: FontPalette.bold18.copyWith(color: Colors.white),
              ),
              SvgPicture.asset(
                Assets.arrowDown,
                color: Colors.white,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<PaymentTypeModel> _buildCurrencyItems(DepositState state) {
    final paymentService = getIt<PaymentFeatureService>();
    final availablePaymentTypes = paymentService.getAvailablePaymentTypes(
      apiWalletCoins: state.walletCoins,
    );
    return availablePaymentTypes;
  }

  Widget _buildDepositForm(DepositState state) {
    final config = getIt<RefactoredAppConfig>();
    final showDepositeTxHash = config.showDepositeTxHash;
    return Container(
      margin: !showDepositeTxHash
          ? EdgeInsets.only(top: 80.h)
          : EdgeInsets.only(top: 0.h),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(32.r),
          topLeft: Radius.circular(32.r),
        ),
        color: myColorScheme(context).cardColor,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Content area that grows with content
          showDepositeTxHash
              ? _layoutWithTxHash(state)
              : _layoutWithoutTxHash(state),
          // Timer always at bottom
          if (!config.showDepositeTxHash)
            _buildOtpTimer2(),
        ],
      ),
    );
  }

  Widget _layoutWithTxHash(DepositState state) => Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (state.walletRechargeAddress != null) ...[
              _buildOtpTimer(state),
              _buildQrCode(state),
              _buildAddressField(state),
            ],
            20.verticalSpace,
            _buildTransactionForm(state),
          ],
        ),
      );
  Widget _layoutWithoutTxHash(DepositState state) => Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (state.walletRechargeAddress != null) ...[
              30.verticalSpace,
              _buildQrCode(state),
              _buildAddressField(state),
            ] else ...[
              const CommonEmpty(),
              30.verticalSpace,
            ]
          ],
        ),
      );

  Widget _buildOtpTimer(DepositState state) {
    return BlocSelector<DepositCubit, DepositState, (int?, PaymentTypeModel?)>(
      selector: (state) => (state.otpTimer, state.paymentType),
      builder: (context, state) {
        if (state.$1 == null) {
          return Padding(
            padding: EdgeInsets.only(top: 10.w),
            child: CommonShimmer(
              width: 200.w,
              height: 60.h,
            ),
          );
        }

        if (state.$1! > 1) {
          return SizedBox(
            width: double.infinity,
            height: 50.h,
            child: OtpTimer(
              width: 50.w,
              height: 50.h,
              color: myColorScheme(context).primaryColor,
              seconds: state.$1,
              onEnd: () {
                final cubit = context.read<DepositCubit>();
                final currentPaymentType = state.$2;

                if (currentPaymentType != null) {
                  cubit.fetchWalletRechargeAddress(currentPaymentType);
                } else {
                  // Fallback to default payment type logic
                  final paymentService = getIt<PaymentFeatureService>();
                  if (paymentService.shouldFetchWalletCoinsFromApi) {
                    final defaultPaymentType = paymentService.getDefaultPaymentType(
                      apiWalletCoins: cubit.state.walletCoins,
                    );
                    if (defaultPaymentType != null) {
                      cubit.fetchWalletRechargeAddress(defaultPaymentType);
                    }
                  } else {
                    final defaultPaymentType =
                        paymentService.getDefaultPaymentType();
                    if (defaultPaymentType != null) {
                      cubit.fetchWalletRechargeAddress(defaultPaymentType);
                    }
                  }
                }
              },
            ),
          );
        }

        return Padding(
          padding: EdgeInsets.only(top: 10.w),
          child: CommonShimmer(
            width: 200.w,
            height: 60.h,
          ),
        );
      },
    );
  }

  Widget _buildOtpTimer2() {
    return BlocSelector<DepositCubit, DepositState, (int?, PaymentTypeModel?)>(
      selector: (state) => (state.otpTimer, state.paymentType),
      builder: (context, state) {
        if (state.$1 == null) {
          return const SizedBox.shrink();
        }
        if (state.$1! > 1) {
          return Container(
            width: 1.sw,
            height: 60.h,
            color: myColorScheme(context).primaryColor,
            child: OtpTimer(
              width: 50.w,
              height: 50.h,
              seconds: state.$1,
              onEnd: () {
                final cubit = context.read<DepositCubit>();
                final currentPaymentType = state.$2;

                if (currentPaymentType != null) {
                  cubit.fetchWalletRechargeAddress(currentPaymentType);
                } else {
                  // Fallback to default payment type logic
                  final paymentService = getIt<PaymentFeatureService>();
                  if (paymentService.shouldFetchWalletCoinsFromApi) {
                    final defaultPaymentType = paymentService.getDefaultPaymentType(
                      apiWalletCoins: cubit.state.walletCoins,
                    );
                    if (defaultPaymentType != null) {
                      cubit.fetchWalletRechargeAddress(defaultPaymentType);
                    }
                  } else {
                    final defaultPaymentType =
                        paymentService.getDefaultPaymentType();
                    if (defaultPaymentType != null) {
                      cubit.fetchWalletRechargeAddress(defaultPaymentType);
                    }
                  }
                }
              },
            ),
          );
        } else {
          return CommonShimmer(
            width: 1.sw,
            height: 60.h,
          );
        }
      },
    );
  }

  Widget _buildQrCode(DepositState state) {
    return Column(
      children: [
        Text(
          StringConstants.rechargeQr.tr(),
          style: FontPalette.bold20.copyWith(
            color: myColorScheme(context).titleColor,
            fontWeight: FontWeight.w400,
          ),
        ),
        10.verticalSpace,
        Container(
          width: 150.w,
          height: 150.h,
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: const [
              BoxShadow(
                offset: Offset(0, -1),
                blurRadius: 15,
                color: Color.fromRGBO(0, 0, 0, 0.1),
              ),
            ],
          ),
          child: QrImageView(
            data: state.walletRechargeAddress?.qrCode ?? '',
            version: QrVersions.auto,
            size: 150.r,
            foregroundColor:
                myColorScheme(context).titleColor ?? ColorPalette.titleColor,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressField(DepositState state) {
    return Column(
      children: [
        20.verticalSpace,
        Text(
          StringConstants.rechargeAddress.tr(),
          style: FontPalette.bold20.copyWith(
            color: myColorScheme(context).titleColor,
            fontWeight: FontWeight.w400,
          ),
        ),
        23.verticalSpace,
        Container(
          width: double.infinity,
          margin: EdgeInsets.symmetric(horizontal: 20.w),
          height: 50.h,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            color: myColorScheme(context).backgroundColor1,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(14, 0, 0, 0).r,
                  child: Text(
                    state.walletRechargeAddress?.address ?? '',
                    style: FontPalette.normal12.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                ),
              ),
              _buildCopyButton(state),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTip() {
    return GestureDetector(
      onTap: () => _navigateToTipsScreen(),
      child: Container(
        width: double.infinity,
        height: 40.h,
        decoration: BoxDecoration(
          color: const Color(0xffd0e4ff),
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Row(
          children: [
            10.horizontalSpace,
            Icon(
              Icons.info_outline,
              color: ColorPalette.primaryVar1,
              size: 20.r,
            ),
            10.horizontalSpace,
            Expanded(
              child: Text(
                StringConstants.depositTip.tr(),
                style: FontPalette.normal12.copyWith(
                  color: myColorScheme(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToTipsScreen() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const TipsBottomSheet(),
    );
  }

  Widget _buildCopyButton(DepositState state) {
    return InkWell(
      onTap: () => _handleCopyAddress(state),
      child: Container(
        width: 60.w,
        height: 60.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(const Radius.circular(10).r),
          color: myColorScheme(context).primaryVar4,
        ),
        child: Center(
          child: Text(
            StringConstants.copy.tr(),
            style: FontPalette.bold12.copyWith(color: Colors.white),
          ),
        ),
      ),
    );
  }

  void _handleCopyAddress(DepositState state) {
    Clipboard.setData(ClipboardData(
      text: state.walletRechargeAddress?.address ?? '',
    ));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(StringConstants.copiedClipboard.tr())),
    );
  }

  Widget _buildTransactionForm(DepositState state) {
    return Form(
      key: _formGlobalKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          children: [
            CommonTextField(
              textInputType: TextInputType.text,
              textInputAction: TextInputAction.next,
              labelText: StringConstants.transactionHash.tr(),
              hintText: StringConstants.enterTransactionHash.tr(),
              controller: _addressController,
              validator: (_) => validateAddress(
                  _addressController.text.trim(),
                  getIt<PaymentFeatureService>().getPaymentTypeFromCode(
                      state.paymentType?.code ?? '')),
              onChanged: (_) => _onTextChange(),
            ),
            10.verticalSpace,
            _buildTip(),
            10.verticalSpace,
            _buildSubmitButton(state),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton(DepositState state) {
    return BlocListener<DepositCubit, DepositState>(
      listenWhen: (previous, current) =>
          previous.createPayOrderFetchStatus !=
          current.createPayOrderFetchStatus,
      listener: (context, state) {
        if (state.createPayOrderFetchStatus == DataStatus.success) {
          _showSuccessDialog();
        }
        if (state.createPayOrderFetchStatus == DataStatus.failed) {
          NetworkHelper.handleMessage(
            state.error,
            context,
            type: HandleTypes.customDialog,
            snackBarType: SnackBarType.error,
          );
        }
      },
      child: ValueListenableBuilder<bool>(
        valueListenable: _buttonEnabledNotifier,
        builder: (context, isEnabled, child) {
          return CustomButton(
            label: StringConstants.submitRecharge.tr(),
            width: double.infinity,
            height: 50.h,
            isOutlined: false,
            borderRadiusUser: 10,
            isEnabled: isEnabled,
            isLoading: state.createPayOrderFetchStatus == DataStatus.loading,
            onPressed: () => _handleSubmit(state),
          );
        },
      ),
    );
  }

  void _showSuccessDialog() {
    CommonFunctions.showDialogPopUp(
      context,
      CustomAlertDialog(
        title: StringConstants.rechargeOrderSubmittedSuccessfully.tr(),
        actionButtonText: StringConstants.ok.tr(),
        buttonBackGroundColor: myColorScheme(context).primaryColor,
        onActionButtonPressed: () {
          Navigator.pop(context);
          Navigator.pop(context);
          _initialFunction();
          context.read<HomeCubit>().getBalance();
        },
        headerImage: Assets.alertSuccess,
        isLoading: false,
        messageTextStyle: FontPalette.semiBold30.copyWith(fontSize: 29.sp),
      ),
      barrierDismissible: false,
    );
  }

  void _handleSubmit(DepositState state) {
    if (_formGlobalKey.currentState!.validate()) {
      context.read<DepositCubit>().createPayOrder(
            trxHash: _addressController.text,
          );
    }
  }
}
