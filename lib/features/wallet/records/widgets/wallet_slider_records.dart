import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/color_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';
import '../../../../core/widgets/common_wallet_slider_item.dart';
import '../../../auth/account_info/logic/wallet/wallet_cubit.dart';
import '../../../home/<USER>/models/balance/balance_model.dart';

class WalletSliderRecords extends StatelessWidget {
  final bool? isLoading;
  final double width;
  final double fullWidth;
  final double height;
  final BalanceData balance;

  const WalletSliderRecords({
    super.key,
    this.isLoading,
    required this.width,
    required this.fullWidth,
    required this.height,
    required this.balance,
  });

  @override
  Widget build(BuildContext context) {
    return BlocSelector<WalletCubit, WalletState, int>(
      selector: (state) => state.recordSliderIndex,
      builder: (context, recordSliderIndex) {
        // Create wallet items list, conditionally excluding trading wallet for SIS flavor
        final walletItems = <String>[
          StringConstants.fundingWallet,
          if (AppConfig.instance.showTradingWallet)
            StringConstants.tradingWallet,
        ];

        return Column(
          children: [
            CarouselSlider(
              options: CarouselOptions(
                enableInfiniteScroll: false,
                height: height + 8,
                viewportFraction: 1,
                onPageChanged: (index, _) =>
                    context.read<WalletCubit>().setRecordSliderIndex(index),
              ),
              items: walletItems
                  .asMap()
                  .map(
                    (index, title) => MapEntry(
                      index,
                      CommonWalletSliderItem(
                        isLoading: isLoading,
                        width: width,
                        balance: balance,
                        title: title,
                        isCommunity: index == 1 &&
                            AppConfig.instance.fetchCommunityRecords,
                      ),
                    ),
                  )
                  .values
                  .toList(),
            ),
            if (walletItems.length > 1) ...[
              19.verticalSpace,
              _DotIndicators(
                recordSliderIndex: recordSliderIndex,
                totalItems: walletItems.length,
              ),
            ]
          ],
        );
      },
    );
  }
}

class _DotIndicators extends StatelessWidget {
  final int recordSliderIndex;
  final int totalItems;

  const _DotIndicators({
    required this.recordSliderIndex,
    required this.totalItems,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalItems,
        (index) => Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w),
          child: _DotIndicatorSlider(
            recordSliderIndex: recordSliderIndex,
            index: index,
          ),
        ),
      ),
    );
  }
}

class _DotIndicatorSlider extends StatelessWidget {
  final int index;
  final int recordSliderIndex;

  const _DotIndicatorSlider({
    required this.recordSliderIndex,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    final isActive = recordSliderIndex == index;
    return Container(
      width: isActive ? 20.w : 10.w,
      height: 6.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20).r,
        color: isActive
            ? myColorScheme(context).primaryColor
            : ColorPalette.greyColor2,
      ),
    );
  }
}
