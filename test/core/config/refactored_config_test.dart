import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';

import 'package:sf_app_v2/core/config/environment_config.dart';
import 'package:sf_app_v2/core/config/flavors/sf_flavor_config.dart';
import 'package:sf_app_v2/core/config/feature_configs/payment_feature_config.dart';
import 'package:sf_app_v2/core/config/feature_configs/investment_feature_config.dart';
import 'package:sf_app_v2/core/config/refactored_app_config.dart';

void main() {
  group('Refactored Configuration System', () {
    late RefactoredAppConfig appConfig;
    late PaymentFeatureService paymentService;
    late InvestmentFeatureService investmentService;

    setUp(() {
      final environmentConfig = EnvironmentConfig.debug;
      final flavorConfig = SfFlavorConfig.create();
      paymentService = PaymentFeatureService(flavorConfig.paymentConfig);
      investmentService = InvestmentFeatureService(flavorConfig.investmentConfig);
      
      appConfig = RefactoredAppConfig(
        environmentConfig,
        flavorConfig,
        paymentService,
        investmentService,
      );
    });

    group('Environment Configuration', () {
      test('should provide correct debug environment settings', () {
        expect(appConfig.isDebug, isTrue);
        expect(appConfig.enableLogging, isTrue);
        expect(appConfig.showDebugInfo, isTrue);
        expect(appConfig.baseUrl, equals('https://api.superfuture.world'));
        expect(appConfig.marketWsUrl, equals('wss://api.superfuture.world/ws'));
      });

      test('should provide correct production environment settings', () {
        final prodConfig = EnvironmentConfig.production;
        expect(prodConfig.isDebug, isFalse);
        expect(prodConfig.enableLogging, isFalse);
        expect(prodConfig.showDebugInfo, isFalse);
      });
    });

    group('App Configuration', () {
      test('should provide correct Super Future app settings', () {
        expect(appConfig.appName, equals('Super Future'));
        expect(appConfig.appUrl, equals('https://superfuture.world/'));
        expect(appConfig.icon, equals('assets/logo/sf_app/logo.svg'));
        expect(appConfig.introVideo, equals('assets/splash/sf_app/introVideo.mp4'));
        expect(appConfig.accountType, equals('3'));
        expect(appConfig.defaultLocale, equals(const Locale('en', 'US')));
      });

      test('should provide correct UI feature flags', () {
        expect(appConfig.showDebugVersionTag, isTrue);
        expect(appConfig.showTradingWallet, isTrue);
        expect(appConfig.showTransferPreview, isTrue);
        expect(appConfig.showBenefitRules, isFalse);
        expect(appConfig.showTransfer, isTrue);
        expect(appConfig.showPurchaseProductFields, isFalse);
        expect(appConfig.showMentorVipLevel, isFalse);
        expect(appConfig.showWithdrawHistory, isFalse);
        expect(appConfig.showAppUpdate, isFalse);
        expect(appConfig.disableUpperCasePasswordProtection, isFalse);
      });
    });

    group('Payment Feature Service', () {
      test('should provide correct payment configuration', () {
        expect(paymentService.shouldFetchWalletCoinsFromApi, isFalse);
        expect(paymentService.showAddWalletAddress, isFalse);
        expect(paymentService.showDepositTxHash, isFalse);
      });

      test('should provide available payment types', () {
        final paymentTypes = paymentService.getAvailablePaymentTypes();
        expect(paymentTypes, isNotEmpty);
        expect(paymentTypes.length, equals(2));
        expect(paymentTypes.first.code, equals('TRC20'));
        expect(paymentTypes.last.code, equals('ERC20'));
      });

      test('should provide default payment type', () {
        final defaultType = paymentService.getDefaultPaymentType();
        expect(defaultType, isNotNull);
        expect(defaultType!.code, equals('TRC20'));
      });

      test('should provide static withdraw address types', () {
        final withdrawTypes = paymentService.getStaticWithdrawAddressTypes();
        expect(withdrawTypes, isNotEmpty);
        expect(withdrawTypes.length, equals(4));
      });
    });

    group('Investment Feature Service', () {
      test('should provide correct investment configuration', () {
        expect(investmentService.enableSmartInvestment, isTrue);
        expect(investmentService.shouldFetchFromApi, isFalse);
      });

      test('should provide purchase percentages', () {
        final percentages = investmentService.getPurchasePercentages();
        expect(percentages, isNotEmpty);
        expect(percentages.length, equals(3));
        expect(percentages.map((p) => p.percentage), containsAll(['30', '50', '100']));
      });

      test('should provide default percentage', () {
        final defaultPercentage = investmentService.getDefaultPercentage();
        expect(defaultPercentage, equals('100'));
      });

      test('should validate percentage enablement', () {
        expect(investmentService.isPercentageEnabled('100'), isTrue);
        expect(investmentService.isPercentageEnabled('50'), isFalse);
        expect(investmentService.isPercentageEnabled('30'), isFalse);
      });

      test('should determine when to show purchase percentages', () {
        final shouldShow = investmentService.shouldShowPurchasePercentages();
        expect(shouldShow, isTrue);
      });
    });

    group('Configuration Integration', () {
      test('should provide access to feature services through main config', () {
        expect(appConfig.paymentService, equals(paymentService));
        expect(appConfig.investmentService, equals(investmentService));
      });

      test('should provide supported languages', () {
        final languages = appConfig.supportedLanguages;
        expect(languages, isNotEmpty);
      });
    });
  });
}
